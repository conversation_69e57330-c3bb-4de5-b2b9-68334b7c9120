"""
Test PatientStudyValidator functionality for DICOM PS3.3 C.7.2.2 Patient Study Module.

Comprehensive unit tests validating all Type 2C conditional requirements, enumerated values,
sequence validation, semantic constraints, and cross-field dependencies.
All tests use datasets generated from modules via to_dataset() method per the
composition-based architecture requirements.
"""
import pytest
import pydicom
from pydicom import Dataset
from pyrt_dicom.modules import PatientStudyModule
from pyrt_dicom.validators.modules.patient_study_validator import PatientStudyValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums.patient_study_enums import SmokingStatus, PregnancyStatus, PatientSexNeutered


class TestPatientStudyValidator:
    """Test PatientStudyValidator comprehensive validation functionality."""
    
    def create_basic_patient_study_dataset(self) -> Dataset:
        """Create a basic patient study module dataset for testing."""
        patient_study = PatientStudyModule.from_required_elements()
        return patient_study.to_dataset()
    
    def create_patient_study_with_demographics(self) -> Dataset:
        """Create patient study dataset with demographic information."""
        patient_study = PatientStudyModule.from_required_elements()
        patient_study.with_patient_demographics(
            patients_age="045Y",
            patients_size=1.75,
            patients_weight=70.5,
            smoking_status=SmokingStatus.NO
        )
        return patient_study.to_dataset()
    
    def test_validate_method_signature(self):
        """Test that validate method has correct signature and returns ValidationResult."""
        dataset = self.create_basic_patient_study_dataset()
        config = ValidationConfig()
        
        # Test method exists and is callable
        assert hasattr(PatientStudyValidator, 'validate')
        assert callable(PatientStudyValidator.validate)
        
        # Test it accepts dataset and config parameters
        result = PatientStudyValidator.validate(dataset, config)
        
        # Test it returns ValidationResult
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_validate_empty_dataset_passes(self):
        """Test that empty dataset passes validation (all elements are Type 2C/3)."""
        dataset = self.create_basic_patient_study_dataset()
        result = PatientStudyValidator.validate(dataset)
        
        # Should pass with no errors since all elements are optional
        assert len(result.errors) == 0
        assert isinstance(result, ValidationResult)
    
    def test_validate_with_valid_demographics_passes(self):
        """Test that dataset with valid demographic information passes."""
        dataset = self.create_patient_study_with_demographics()
        result = PatientStudyValidator.validate(dataset)
        
        # Should pass with no errors
        assert len(result.errors) == 0
    
    def test_validate_smoking_status_enumerated_values(self):
        """Test validation of smoking status enumerated values."""
        # Test valid values
        for valid_status in ["YES", "NO", "UNKNOWN"]:
            dataset = self.create_basic_patient_study_dataset()
            dataset.SmokingStatus = valid_status
            result = PatientStudyValidator.validate(dataset)
            assert len(result.errors) == 0, f"Valid smoking status '{valid_status}' should not produce errors"
        
        # Test invalid value
        dataset = self.create_basic_patient_study_dataset()
        dataset.SmokingStatus = "INVALID"
        result = PatientStudyValidator.validate(dataset)
        assert len(result.warnings) > 0, "Invalid smoking status should produce warnings"
        assert any("Smoking Status" in warning for warning in result.warnings)
    
    def test_validate_pregnancy_status_enumerated_values(self):
        """Test validation of pregnancy status enumerated values."""
        # Test valid values
        for valid_status in [1, 2, 3, 4, "1", "2", "3", "4"]:
            dataset = self.create_basic_patient_study_dataset()
            dataset.PregnancyStatus = valid_status
            result = PatientStudyValidator.validate(dataset)
            assert len(result.errors) == 0, f"Valid pregnancy status '{valid_status}' should not produce errors"
        
        # Test invalid value
        dataset = self.create_basic_patient_study_dataset()
        dataset.PregnancyStatus = "5"
        result = PatientStudyValidator.validate(dataset)
        assert len(result.warnings) > 0, "Invalid pregnancy status should produce warnings"
        assert any("Pregnancy Status" in warning for warning in result.warnings)
    
    def test_validate_patient_sex_neutered_enumerated_values(self):
        """Test validation of patient sex neutered enumerated values."""
        # Test valid values
        for valid_status in ["ALTERED", "UNALTERED"]:
            dataset = self.create_basic_patient_study_dataset()
            dataset.PatientSexNeutered = valid_status
            result = PatientStudyValidator.validate(dataset)
            assert len(result.errors) == 0, f"Valid sex neutered status '{valid_status}' should not produce errors"
        
        # Test invalid value
        dataset = self.create_basic_patient_study_dataset()
        dataset.PatientSexNeutered = "INVALID"
        result = PatientStudyValidator.validate(dataset)
        assert len(result.warnings) > 0, "Invalid sex neutered status should produce warnings"
        assert any("Patient's Sex Neutered" in warning for warning in result.warnings)
    
    def test_validate_patient_age_format(self):
        """Test validation of patient age format."""
        # Test valid formats
        valid_ages = ["045Y", "012M", "003W", "090D"]
        for valid_age in valid_ages:
            dataset = self.create_basic_patient_study_dataset()
            dataset.PatientAge = valid_age
            result = PatientStudyValidator.validate(dataset)
            assert len(result.errors) == 0, f"Valid age format '{valid_age}' should not produce errors"
        
        # Test invalid formats
        invalid_ages = ["45Y", "45", "Y45", "45 Y", "045X"]
        for invalid_age in invalid_ages:
            dataset = self.create_basic_patient_study_dataset()
            dataset.PatientAge = invalid_age
            result = PatientStudyValidator.validate(dataset)
            assert len(result.errors) > 0, f"Invalid age format '{invalid_age}' should produce errors"
            assert any("Patient Age" in error for error in result.errors)
    
    def test_validate_last_menstrual_date_format(self):
        """Test validation of last menstrual date format."""
        # Test valid format
        dataset = self.create_basic_patient_study_dataset()
        dataset.LastMenstrualDate = "20240315"
        result = PatientStudyValidator.validate(dataset)
        assert len(result.errors) == 0, "Valid date format should not produce errors"
        
        # Test invalid formats
        invalid_dates = ["2024-03-15", "20240315T", "240315", "20241315", "20240332"]
        for invalid_date in invalid_dates:
            dataset = self.create_basic_patient_study_dataset()
            dataset.LastMenstrualDate = invalid_date
            result = PatientStudyValidator.validate(dataset)
            assert len(result.errors) > 0, f"Invalid date format '{invalid_date}' should produce errors"
            assert any("Last Menstrual Date" in error for error in result.errors)

    def test_validate_physical_measurement_ranges(self):
        """Test validation of physical measurement ranges."""
        # Test valid measurements
        dataset = self.create_basic_patient_study_dataset()
        dataset.PatientSize = 1.75  # meters
        dataset.PatientWeight = 70.5  # kg
        dataset.PatientBodyMassIndex = 23.0  # kg/m²
        result = PatientStudyValidator.validate(dataset)
        assert len(result.errors) == 0, "Valid measurements should not produce errors"

        # Test extreme but possible measurements (should produce warnings)
        dataset = self.create_basic_patient_study_dataset()
        dataset.PatientSize = 0.05  # 5cm - very small
        result = PatientStudyValidator.validate(dataset)
        assert len(result.warnings) > 0, "Extreme measurements should produce warnings"
        assert any("Patient Size" in warning for warning in result.warnings)

        # Test invalid measurements (should produce errors)
        # Note: We can't assign invalid strings to numeric DICOM fields due to pydicom validation
        # So we test the validator's range checking instead
        dataset = self.create_basic_patient_study_dataset()
        dataset.PatientWeight = -5.0  # Negative weight - invalid
        result = PatientStudyValidator.validate(dataset)
        assert len(result.warnings) > 0, "Invalid measurements should produce warnings"
        assert any("Patient Weight" in warning for warning in result.warnings)

    def test_validate_conditional_requirements_non_human_organism(self):
        """Test Type 2C conditional requirement for non-human organisms."""
        # Test with species information but missing sex neutered (should error)
        dataset = self.create_basic_patient_study_dataset()
        dataset.PatientSpeciesDescription = "Canine"
        result = PatientStudyValidator.validate(dataset)
        assert len(result.errors) > 0, "Missing sex neutered for non-human should produce error"
        assert any("Patient's Sex Neutered" in error and "non-human organism" in error for error in result.errors)

        # Test with species information and sex neutered (should pass)
        dataset = self.create_basic_patient_study_dataset()
        dataset.PatientSpeciesDescription = "Canine"
        dataset.PatientSexNeutered = "ALTERED"
        result = PatientStudyValidator.validate(dataset)
        # Should not have the conditional requirement error
        conditional_errors = [e for e in result.errors if "non-human organism" in e]
        assert len(conditional_errors) == 0, "Complete non-human organism info should not produce conditional errors"

    def test_validate_sex_parameters_conditional_requirements(self):
        """Test Type 2C conditional requirements for sex parameters."""
        # Create sequence with "Specified" code but missing comment/reference
        dataset = self.create_basic_patient_study_dataset()

        # Create sex parameters sequence with "Specified" code
        sex_param_item = Dataset()
        code_item = Dataset()
        code_item.CodeValue = "131232"
        code_item.CodingSchemeDesignator = "DCM"
        sex_param_item.SexParametersForClinicalUseCategoryCodeSequence = [code_item]
        # Missing comment and reference (should error)

        dataset.SexParametersForClinicalUseCategorySequence = [sex_param_item]
        result = PatientStudyValidator.validate(dataset)

        assert len(result.errors) >= 2, "Missing comment and reference should produce errors"
        comment_errors = [e for e in result.errors if "Comment" in e and "Type 2C" in e]
        reference_errors = [e for e in result.errors if "Reference" in e and "Type 2C" in e]
        assert len(comment_errors) > 0, "Missing comment should produce error"
        assert len(reference_errors) > 0, "Missing reference should produce error"

        # Test with complete information (should pass)
        sex_param_item.SexParametersForClinicalUseCategoryComment = "Clinical context comment"
        sex_param_item.SexParametersForClinicalUseCategoryReference = "Reference source"
        dataset.SexParametersForClinicalUseCategorySequence = [sex_param_item]
        result = PatientStudyValidator.validate(dataset)

        conditional_errors = [e for e in result.errors if "Type 2C" in e and "Specified" in e]
        assert len(conditional_errors) == 0, "Complete sex parameters info should not produce conditional errors"

    def test_validate_sequence_structure_requirements(self):
        """Test sequence structure validation."""
        dataset = self.create_basic_patient_study_dataset()

        # Test Gender Identity Sequence with missing required sub-attribute
        gender_item = Dataset()
        # Missing GenderIdentityCodeSequence (Type 1 within sequence)
        dataset.GenderIdentitySequence = [gender_item]
        result = PatientStudyValidator.validate(dataset)

        assert len(result.errors) > 0, "Missing required sequence sub-attribute should produce error"
        assert any("Gender Identity Code Sequence" in error and "Type 1" in error for error in result.errors)

        # Test with complete sequence item
        code_item = Dataset()
        code_item.CodeValue = "F"
        code_item.CodeMeaning = "Female"
        gender_item.GenderIdentityCodeSequence = [code_item]
        dataset.GenderIdentitySequence = [gender_item]
        result = PatientStudyValidator.validate(dataset)

        sequence_errors = [e for e in result.errors if "Gender Identity Code Sequence" in e]
        assert len(sequence_errors) == 0, "Complete sequence should not produce structure errors"

    def test_validate_cross_field_dependencies(self):
        """Test cross-field dependency validation."""
        dataset = self.create_basic_patient_study_dataset()

        # Test pregnancy status with male patient (should warn)
        dataset.PatientSex = "M"  # From Patient Module
        dataset.PregnancyStatus = 1  # Not pregnant
        result = PatientStudyValidator.validate(dataset)

        pregnancy_warnings = [w for w in result.warnings if "Pregnancy Status" in w and "sex" in w]
        assert len(pregnancy_warnings) > 0, "Pregnancy status for male patient should produce warning"

        # Test medical alerts vs allergies consistency
        dataset = self.create_basic_patient_study_dataset()
        dataset.MedicalAlerts = "Patient has severe allergies to penicillin"
        dataset.Allergies = "None known"
        result = PatientStudyValidator.validate(dataset)

        consistency_warnings = [w for w in result.warnings if "Medical Alerts" in w and "Allergies" in w]
        assert len(consistency_warnings) > 0, "Inconsistent medical alerts and allergies should produce warning"

    def test_validate_effective_datetime_range(self):
        """Test effective date/time range validation for sequences."""
        dataset = self.create_basic_patient_study_dataset()

        # Create sequence item with invalid date range
        gender_item = Dataset()
        code_item = Dataset()
        code_item.CodeValue = "F"
        gender_item.GenderIdentityCodeSequence = [code_item]
        gender_item.EffectiveStartDateTime = "20240315120000"
        gender_item.EffectiveStopDateTime = "20240314120000"  # Earlier than start

        dataset.GenderIdentitySequence = [gender_item]
        result = PatientStudyValidator.validate(dataset)

        datetime_warnings = [w for w in result.warnings if "Effective Start DateTime" in w and "after" in w]
        assert len(datetime_warnings) > 0, "Invalid date/time range should produce warning"

    def test_validate_visit_information_completeness(self):
        """Test visit information completeness validation."""
        dataset = self.create_basic_patient_study_dataset()

        # Test partial visit information (should warn)
        dataset.AdmissionID = "ADM001"
        # Missing other visit fields
        result = PatientStudyValidator.validate(dataset)

        completeness_warnings = [w for w in result.warnings if "Partial visit information" in w]
        assert len(completeness_warnings) > 0, "Partial visit information should produce warning"

        # Test complete visit information (should not warn about completeness)
        dataset.ReasonForVisit = "Annual checkup"
        dataset.ServiceEpisodeID = "EP001"
        result = PatientStudyValidator.validate(dataset)

        completeness_warnings = [w for w in result.warnings if "Partial visit information" in w]
        assert len(completeness_warnings) == 0, "Complete visit information should not produce completeness warning"

    def test_validation_config_options(self):
        """Test that validation configuration options work correctly."""
        dataset = self.create_basic_patient_study_dataset()
        dataset.SmokingStatus = "INVALID"  # Should produce warning
        dataset.PatientAge = "45Y"  # Should produce error

        # Test with all validations enabled (default)
        config = ValidationConfig()
        result = PatientStudyValidator.validate(dataset, config)
        assert len(result.warnings) > 0, "Should have enumerated value warnings"
        assert len(result.errors) > 0, "Should have semantic constraint errors"

        # Test with enumerated value validation disabled
        config = ValidationConfig(check_enumerated_values=False)
        result = PatientStudyValidator.validate(dataset, config)
        enum_warnings = [w for w in result.warnings if "Smoking Status" in w]
        assert len(enum_warnings) == 0, "Should not have enumerated value warnings when disabled"

        # Test with semantic validation disabled
        config = ValidationConfig(validate_semantic_constraints=False)
        result = PatientStudyValidator.validate(dataset, config)
        age_errors = [e for e in result.errors if "Patient Age" in e]
        assert len(age_errors) == 0, "Should not have age format errors when semantic validation disabled"

    def test_validate_all_sequence_types(self):
        """Test validation of all sequence types in the module."""
        dataset = self.create_basic_patient_study_dataset()

        # Test Person Names to Use Sequence
        name_item = Dataset()
        # Missing NameToUse (should error)
        dataset.PersonNamesToUseSequence = [name_item]
        result = PatientStudyValidator.validate(dataset)
        name_errors = [e for e in result.errors if "Name to Use" in e and "Type 1" in e]
        assert len(name_errors) > 0, "Missing Name to Use should produce error"

        # Test Third Person Pronouns Sequence
        pronoun_item = Dataset()
        # Missing PronounCodeSequence (should error)
        dataset.ThirdPersonPronounsSequence = [pronoun_item]
        result = PatientStudyValidator.validate(dataset)
        pronoun_errors = [e for e in result.errors if "Pronoun Code Sequence" in e and "Type 1" in e]
        assert len(pronoun_errors) > 0, "Missing Pronoun Code Sequence should produce error"

    def test_validate_with_none_config(self):
        """Test validation with None config (should use defaults)."""
        dataset = self.create_patient_study_with_demographics()
        result = PatientStudyValidator.validate(dataset, None)

        # Should work without errors
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0, "Valid dataset should pass with default config"
