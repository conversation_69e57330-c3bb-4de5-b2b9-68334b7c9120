"""
Test ImagePixelValidator functionality.

Tests comprehensive validation of the Image Pixel Module according to DICOM PS3.3 C.7.6.3.
Validates Type 1 requirements, Type 1C conditional logic, enumerated values, and pixel data consistency.
"""

import pytest
import pydicom
from pyrt_dicom.validators.modules.image_pixel_validator import ImagePixelValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators.validation_result import ValidationResult


class TestImagePixelValidator:
    """Test ImagePixelValidator comprehensive validation logic."""
    
    def create_valid_dataset(self) -> pydicom.Dataset:
        """Create a valid dataset with all required Image Pixel Module elements."""
        dataset = pydicom.Dataset()
        dataset.SamplesPerPixel = 1
        dataset.PhotometricInterpretation = "MONOCHROME2"
        dataset.Rows = 64
        dataset.Columns = 64
        dataset.BitsAllocated = 16
        dataset.BitsStored = 16
        dataset.HighBit = 15
        dataset.PixelRepresentation = 0
        dataset.PixelData = b'\x00' * (64 * 64 * 2)  # 16-bit data
        return dataset
    
    def test_validate_returns_validation_result(self):
        """Test that validate method returns proper ValidationResult."""
        dataset = self.create_valid_dataset()
        result = ImagePixelValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_valid_dataset_passes_validation(self):
        """Test that a valid dataset passes validation without errors."""
        dataset = self.create_valid_dataset()
        result = ImagePixelValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert isinstance(result.warnings, list)  # May have warnings but no errors
    
    def test_missing_required_elements_validation(self):
        """Test validation of missing Type 1 required elements."""
        dataset = pydicom.Dataset()
        result = ImagePixelValidator.validate(dataset)
        
        # Should have errors for all 8 required elements
        assert len(result.errors) >= 8
        
        # Check specific required elements
        error_messages = ' '.join(result.errors)
        required_elements = [
            'SamplesPerPixel', 'PhotometricInterpretation', 'Rows', 'Columns',
            'BitsAllocated', 'BitsStored', 'HighBit', 'PixelRepresentation'
        ]
        
        for element in required_elements:
            assert element in error_messages
            assert "Type 1" in error_messages
    
    def test_samples_per_pixel_validation(self):
        """Test Samples per Pixel validation."""
        dataset = self.create_valid_dataset()
        
        # Test invalid values
        invalid_values = [0, -1, "invalid", None]
        for invalid_value in invalid_values:
            dataset.SamplesPerPixel = invalid_value
            result = ImagePixelValidator.validate(dataset)
            assert any("Samples per Pixel" in error and "positive integer" in error 
                     for error in result.errors)
    
    def test_image_dimensions_validation(self):
        """Test Rows and Columns validation."""
        dataset = self.create_valid_dataset()
        
        # Test invalid rows
        dataset.Rows = 0
        result = ImagePixelValidator.validate(dataset)
        assert any("Rows" in error and "positive integer" in error for error in result.errors)
        
        # Test invalid columns
        dataset.Rows = 64  # Reset
        dataset.Columns = -1
        result = ImagePixelValidator.validate(dataset)
        assert any("Columns" in error and "positive integer" in error for error in result.errors)
    
    def test_bits_allocated_validation(self):
        """Test Bits Allocated validation."""
        dataset = self.create_valid_dataset()
        
        # Test invalid values (not 1 or multiple of 8)
        invalid_values = [0, 3, 5, 7, 9, 15]
        for invalid_value in invalid_values:
            dataset.BitsAllocated = invalid_value
            result = ImagePixelValidator.validate(dataset)
            assert any("Bits Allocated" in error and ("1 or a multiple of 8" in error or "positive integer" in error)
                     for error in result.errors)
        
        # Test valid values
        valid_values = [1, 8, 16, 24, 32]
        for valid_value in valid_values:
            dataset.BitsAllocated = valid_value
            dataset.BitsStored = valid_value
            dataset.HighBit = valid_value - 1
            result = ImagePixelValidator.validate(dataset)
            # Should not have bits allocated errors
            assert not any("Bits Allocated" in error and "multiple of 8" in error 
                          for error in result.errors)
    
    def test_bit_relationships_validation(self):
        """Test relationships between Bits Allocated, Bits Stored, and High Bit."""
        dataset = self.create_valid_dataset()
        
        # Test Bits Stored > Bits Allocated
        dataset.BitsAllocated = 16
        dataset.BitsStored = 20
        dataset.HighBit = 19
        result = ImagePixelValidator.validate(dataset)
        assert any("Bits Stored" in error and "cannot exceed" in error and "Bits Allocated" in error
                  for error in result.errors)
        
        # Test High Bit != Bits Stored - 1
        dataset.BitsStored = 12  # Reset to valid
        dataset.HighBit = 10     # Should be 11
        result = ImagePixelValidator.validate(dataset)
        assert any("High Bit" in error and "one less than Bits Stored" in error
                  for error in result.errors)
    
    def test_pixel_data_conditional_requirements(self):
        """Test pixel data Type 1C conditional requirements."""
        dataset = self.create_valid_dataset()
        
        # Test missing both pixel data and provider URL
        del dataset.PixelData
        result = ImagePixelValidator.validate(dataset)
        assert any("Either Pixel Data" in error and "Pixel Data Provider URL" in error
                  for error in result.errors)
        
        # Test having both (should generate warning)
        dataset.PixelData = b'\x00' * 100
        dataset.PixelDataProviderURL = "http://example.com/pixel_data"
        result = ImagePixelValidator.validate(dataset)
        assert any("Both Pixel Data" in warning and "mutually exclusive" in warning
                  for warning in result.warnings)
    
    def test_planar_configuration_conditional_requirements(self):
        """Test planar configuration Type 1C conditional requirements."""
        dataset = self.create_valid_dataset()
        
        # Test RGB image missing planar configuration
        dataset.SamplesPerPixel = 3
        dataset.PhotometricInterpretation = "RGB"
        result = ImagePixelValidator.validate(dataset)
        assert any("Missing Planar Configuration" in error and "Samples per Pixel" in error
                  for error in result.errors)
        
        # Test monochrome image with planar configuration (should warn)
        dataset.SamplesPerPixel = 1
        dataset.PhotometricInterpretation = "MONOCHROME2"
        dataset.PlanarConfiguration = 0
        result = ImagePixelValidator.validate(dataset)
        assert any("should not be present" in warning and "Samples per Pixel" in warning
                  for warning in result.warnings)

    def test_palette_color_conditional_requirements(self):
        """Test palette color lookup table Type 1C conditional requirements."""
        dataset = self.create_valid_dataset()
        dataset.PhotometricInterpretation = "PALETTE COLOR"

        # Test missing palette color attributes
        result = ImagePixelValidator.validate(dataset)
        assert any("Missing required palette color attributes" in error
                  for error in result.errors)

        # Add all required palette attributes
        dataset.RedPaletteColorLookupTableDescriptor = [256, 0, 16]
        dataset.GreenPaletteColorLookupTableDescriptor = [256, 0, 16]
        dataset.BluePaletteColorLookupTableDescriptor = [256, 0, 16]
        dataset.RedPaletteColorLookupTableData = b'\x00' * 512
        dataset.GreenPaletteColorLookupTableData = b'\x00' * 512
        dataset.BluePaletteColorLookupTableData = b'\x00' * 512

        result = ImagePixelValidator.validate(dataset)
        # Should not have palette color errors
        assert not any("palette color" in error.lower() for error in result.errors)

    def test_enumerated_values_validation(self):
        """Test enumerated values validation."""
        dataset = self.create_valid_dataset()

        # Test invalid photometric interpretation
        dataset.PhotometricInterpretation = "INVALID_PHOTOMETRIC"
        result = ImagePixelValidator.validate(dataset)
        assert any("Invalid Photometric Interpretation" in error
                  for error in result.errors)

        # Test invalid planar configuration
        dataset.PhotometricInterpretation = "RGB"
        dataset.SamplesPerPixel = 3
        dataset.PlanarConfiguration = 2  # Invalid value
        result = ImagePixelValidator.validate(dataset)
        assert any("Invalid Planar Configuration" in error
                  for error in result.errors)

        # Test invalid pixel representation
        dataset.PlanarConfiguration = 0  # Reset to valid
        dataset.PixelRepresentation = 2  # Invalid value
        result = ImagePixelValidator.validate(dataset)
        assert any("Invalid Pixel Representation" in error
                  for error in result.errors)

    def test_photometric_constraints_validation(self):
        """Test photometric interpretation constraints on samples per pixel."""
        dataset = self.create_valid_dataset()

        # Test monochrome with wrong samples per pixel
        dataset.PhotometricInterpretation = "MONOCHROME1"
        dataset.SamplesPerPixel = 3
        result = ImagePixelValidator.validate(dataset)
        assert any("requires Samples per Pixel = 1" in error
                  for error in result.errors)

        # Test RGB with wrong samples per pixel
        dataset.PhotometricInterpretation = "RGB"
        dataset.SamplesPerPixel = 1
        result = ImagePixelValidator.validate(dataset)
        assert any("requires Samples per Pixel = 3" in error
                  for error in result.errors)

        # Test palette color with wrong samples per pixel
        dataset.PhotometricInterpretation = "PALETTE COLOR"
        dataset.SamplesPerPixel = 3
        result = ImagePixelValidator.validate(dataset)
        assert any("requires Samples per Pixel = 1" in error
                  for error in result.errors)

    def test_ybr_format_constraints_validation(self):
        """Test YBR format specific constraints."""
        dataset = self.create_valid_dataset()

        # Test YBR_FULL_422 with wrong planar configuration
        dataset.PhotometricInterpretation = "YBR_FULL_422"
        dataset.SamplesPerPixel = 3
        dataset.PlanarConfiguration = 1  # Should be 0
        result = ImagePixelValidator.validate(dataset)
        assert any("YBR_FULL_422" in error and "Planar Configuration = 0" in error
                  for error in result.errors)

        # Test YBR_FULL_422 with odd columns
        dataset.PlanarConfiguration = 0  # Reset to valid
        dataset.Columns = 63  # Odd number
        result = ImagePixelValidator.validate(dataset)
        assert any("YBR_FULL_422" in error and "even number of Columns" in error
                  for error in result.errors)

        # Test compressed-only YBR formats (should warn)
        compressed_formats = ["YBR_PARTIAL_420", "YBR_ICT", "YBR_RCT"]
        for format_name in compressed_formats:
            dataset.PhotometricInterpretation = format_name
            dataset.Columns = 64  # Reset to even
            result = ImagePixelValidator.validate(dataset)
            assert any("compressed transfer syntaxes" in warning
                      for warning in result.warnings)

    def test_palette_descriptor_consistency_validation(self):
        """Test palette color descriptor consistency validation."""
        dataset = self.create_valid_dataset()
        dataset.PhotometricInterpretation = "PALETTE COLOR"

        # Add palette descriptors with inconsistent first values
        dataset.RedPaletteColorLookupTableDescriptor = [256, 0, 16]
        dataset.GreenPaletteColorLookupTableDescriptor = [128, 0, 16]  # Different first value
        dataset.BluePaletteColorLookupTableDescriptor = [256, 0, 16]
        dataset.RedPaletteColorLookupTableData = b'\x00' * 512
        dataset.GreenPaletteColorLookupTableData = b'\x00' * 256
        dataset.BluePaletteColorLookupTableData = b'\x00' * 512

        result = ImagePixelValidator.validate(dataset)
        assert any("same first value" in error and "number of entries" in error
                  for error in result.errors)

        # Test inconsistent second values
        dataset.GreenPaletteColorLookupTableDescriptor = [256, 10, 16]  # Different second value
        result = ImagePixelValidator.validate(dataset)
        assert any("same second value" in error and "first input value" in error
                  for error in result.errors)

        # Test inconsistent third values
        dataset.GreenPaletteColorLookupTableDescriptor = [256, 0, 8]  # Different third value
        result = ImagePixelValidator.validate(dataset)
        assert any("same third value" in error and "bits per entry" in error
                  for error in result.errors)

    def test_extended_offset_table_validation(self):
        """Test extended offset table conditional requirements."""
        dataset = self.create_valid_dataset()

        # Test Extended Offset Table without Lengths
        dataset.ExtendedOffsetTable = b'\x00\x00\x00\x00\x00\x00\x00\x00'
        result = ImagePixelValidator.validate(dataset)
        assert any("Extended Offset Table Lengths" in error and "Required when" in error
                  for error in result.errors)

        # Test Extended Offset Table without Pixel Data
        del dataset.PixelData
        dataset.PixelDataProviderURL = "http://example.com/pixel_data"
        result = ImagePixelValidator.validate(dataset)
        assert any("Extended Offset Table" in error and "Pixel Data" in error
                  for error in result.errors)

    def test_pixel_padding_validation(self):
        """Test pixel padding range limit validation."""
        dataset = self.create_valid_dataset()

        # Test Pixel Padding Range Limit (should warn about dependency)
        dataset.PixelPaddingRangeLimit = 65535
        result = ImagePixelValidator.validate(dataset)
        assert any("Pixel Padding Range Limit" in warning and "General Equipment Module" in warning
                  for warning in result.warnings)

    def test_color_profile_constraints_validation(self):
        """Test ICC Profile and Color Space constraints."""
        dataset = self.create_valid_dataset()

        # Test ICC Profile with Optical Path Sequence
        dataset.ICCProfile = b'\x00' * 100
        dataset.OpticalPathSequence = [pydicom.Dataset()]
        result = ImagePixelValidator.validate(dataset)
        assert any("ICC Profile" in error and "Optical Path Sequence" in error
                  for error in result.errors)

        # Test Color Space with Optical Path Sequence
        del dataset.ICCProfile
        dataset.ColorSpace = "RGB"
        result = ImagePixelValidator.validate(dataset)
        assert any("Color Space" in error and "Optical Path Sequence" in error
                  for error in result.errors)

        # Test Color Space with ICC Profile (should warn about consistency)
        del dataset.OpticalPathSequence
        dataset.ICCProfile = b'\x00' * 100
        result = ImagePixelValidator.validate(dataset)
        assert any("Color Space" in warning and "consistent" in warning
                  for warning in result.warnings)

    def test_validation_config_support(self):
        """Test validation configuration support."""
        dataset = self.create_valid_dataset()
        dataset.PhotometricInterpretation = "INVALID_VALUE"
        dataset.SamplesPerPixel = 3  # Wrong for monochrome

        # Test with conditional requirements disabled
        config = ValidationConfig(validate_conditional_requirements=False)
        result = ImagePixelValidator.validate(dataset, config)
        # Should still have Type 1 and enumerated value errors, but fewer conditional errors
        assert any("Invalid Photometric Interpretation" in error for error in result.errors)

        # Test with enumerated values disabled
        config = ValidationConfig(check_enumerated_values=False)
        result = ImagePixelValidator.validate(dataset, config)
        # Should not have enumerated value errors
        assert not any("Invalid Photometric Interpretation" in error for error in result.errors)

    def test_error_message_quality(self):
        """Test error message quality and actionable guidance."""
        dataset = pydicom.Dataset()
        result = ImagePixelValidator.validate(dataset)

        # Check that error messages include:
        # 1. DICOM tag references
        # 2. Clear descriptions
        # 3. DICOM standard references
        # 4. Actionable guidance

        error_text = ' '.join(result.errors)

        # Should include DICOM tags
        assert "0028,0002" in error_text  # SamplesPerPixel
        assert "0028,0004" in error_text  # PhotometricInterpretation

        # Should include DICOM standard references
        assert "DICOM PS3.3" in error_text
        assert "Type 1" in error_text

        # Should include clear descriptions
        assert "required" in error_text.lower()
        assert "missing" in error_text.lower()

    def test_validation_result_structure(self):
        """Test ValidationResult structure and consistency."""
        dataset = self.create_valid_dataset()

        # Test with various validation scenarios
        test_cases = [
            (self.create_valid_dataset(), "valid dataset"),
            (pydicom.Dataset(), "empty dataset"),
        ]

        for test_dataset, description in test_cases:
            result = ImagePixelValidator.validate(test_dataset)

            # Verify result structure
            assert isinstance(result, ValidationResult), f"Failed for {description}"
            assert hasattr(result, 'errors'), f"Missing errors attribute for {description}"
            assert hasattr(result, 'warnings'), f"Missing warnings attribute for {description}"
            assert isinstance(result.errors, list), f"Errors not a list for {description}"
            assert isinstance(result.warnings, list), f"Warnings not a list for {description}"

            # Verify all error messages are strings
            for error in result.errors:
                assert isinstance(error, str), f"Non-string error for {description}: {error}"

            # Verify all warning messages are strings
            for warning in result.warnings:
                assert isinstance(warning, str), f"Non-string warning for {description}: {warning}"

    def test_comprehensive_validation_coverage(self):
        """Test comprehensive validation coverage of all major validation paths."""
        # This test ensures all major validation methods are exercised
        dataset = self.create_valid_dataset()

        # Test all photometric interpretations
        photometric_values = [
            "MONOCHROME1", "MONOCHROME2", "RGB", "YBR_FULL", "YBR_FULL_422",
            "YBR_PARTIAL_420", "YBR_ICT", "YBR_RCT", "PALETTE COLOR", "XYB"
        ]

        for photometric in photometric_values:
            test_dataset = self.create_valid_dataset()
            test_dataset.PhotometricInterpretation = photometric

            # Adjust samples per pixel for photometric interpretation
            if photometric in ["MONOCHROME1", "MONOCHROME2", "PALETTE COLOR"]:
                test_dataset.SamplesPerPixel = 1
            else:
                test_dataset.SamplesPerPixel = 3
                test_dataset.PlanarConfiguration = 0

            # Add palette color tables for PALETTE COLOR
            if photometric == "PALETTE COLOR":
                test_dataset.RedPaletteColorLookupTableDescriptor = [256, 0, 16]
                test_dataset.GreenPaletteColorLookupTableDescriptor = [256, 0, 16]
                test_dataset.BluePaletteColorLookupTableDescriptor = [256, 0, 16]
                test_dataset.RedPaletteColorLookupTableData = b'\x00' * 512
                test_dataset.GreenPaletteColorLookupTableData = b'\x00' * 512
                test_dataset.BluePaletteColorLookupTableData = b'\x00' * 512

            # Validate - should not crash and should return valid result
            result = ImagePixelValidator.validate(test_dataset)
            assert isinstance(result, ValidationResult)

            # For valid configurations, should have minimal errors
            if photometric in ["MONOCHROME1", "MONOCHROME2", "RGB", "PALETTE COLOR"]:
                # These should validate cleanly with proper setup
                type1_errors = [e for e in result.errors if "Type 1" in e]
                assert len(type1_errors) == 0, f"Unexpected Type 1 errors for {photometric}: {type1_errors}"
