"""Overlay Plane Module DICOM validation - PS3.3 C.9.2

This validator implements comprehensive validation for the DICOM Overlay Plane Module
according to PS3.3 Section C.9.2, including all conditional logic requirements and
group-specific tag handling for overlay planes (60xx group elements).

Key validation areas:
- Type 1 required elements validation
- Overlay group-specific tag validation (6000-601E)
- ROI statistics conditional logic (only for ROI overlays)
- Multi-frame overlay conditional logic
- Overlay data size consistency validation
- Enumerated value validation for overlay types and subtypes
"""

from pydicom import Dataset
from pydicom.tag import Tag
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.image_enums import OverlayType, OverlaySubtype


class OverlayPlaneValidator(BaseValidator):
    """Validator for DICOM Overlay Plane Module (PS3.3 C.9.2).

    Provides comprehensive validation of overlay plane modules including:
    - Group-specific overlay tag validation (60xx elements)
    - Conditional logic for ROI statistics and multi-frame overlays
    - DICOM standard compliance for overlay data and attributes
    - Semantic validation of overlay types and subtypes
    """

    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Overlay Plane Module requirements on any pydicom Dataset.

        Performs comprehensive validation of overlay plane elements including
        group-specific tag validation, conditional logic validation, and
        DICOM standard compliance checking.

        Args:
            dataset: pydicom Dataset to validate (must contain overlay plane elements)
            config: Validation configuration options for controlling validation scope

        Returns:
            ValidationResult with detailed errors and warnings for DICOM compliance
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()

        # Find all overlay groups present in the dataset
        overlay_groups = OverlayPlaneValidator._find_overlay_groups(dataset)

        if not overlay_groups:
            result.add_error(
                "No overlay plane elements found in dataset. "
                "Overlay Plane Module requires at least one overlay group (60xx elements)."
            )
            return result

        # Validate each overlay group
        for group in overlay_groups:
            OverlayPlaneValidator._validate_overlay_group(dataset, group, result, config)

        return result

    @staticmethod
    def _find_overlay_groups(dataset: Dataset) -> list[int]:
        """Find all overlay groups present in the dataset.

        Args:
            dataset: pydicom Dataset to search for overlay elements

        Returns:
            list[int]: List of overlay group numbers (e.g., [0x6000, 0x6002])
        """
        overlay_groups = set()

        # Search for overlay-specific tags in the dataset
        for tag in dataset.keys():
            group = tag.group
            # Overlay groups are even numbers from 0x6000 to 0x601E
            if 0x6000 <= group <= 0x601E and group % 2 == 0:
                overlay_groups.add(group)

        return sorted(list(overlay_groups))

    @staticmethod
    def _validate_overlay_group(
        dataset: Dataset,
        group: int,
        result: ValidationResult,
        config: ValidationConfig
    ) -> None:
        """Validate a specific overlay group.

        Args:
            dataset: pydicom Dataset containing overlay elements
            group: Overlay group number (e.g., 0x6000)
            result: ValidationResult to accumulate errors and warnings
            config: Validation configuration options
        """
        # Validate Type 1 requirements for this group
        OverlayPlaneValidator._validate_type1_requirements(dataset, group, result)

        # Validate enumerated values
        if config.check_enumerated_values:
            OverlayPlaneValidator._validate_enumerated_values(dataset, group, result)

        # Validate overlay data consistency
        OverlayPlaneValidator._validate_overlay_data_consistency(dataset, group, result)

        # Validate ROI statistics conditional logic
        OverlayPlaneValidator._validate_roi_statistics_conditional_logic(dataset, group, result)

        # Validate multi-frame overlay conditional logic
        OverlayPlaneValidator._validate_multi_frame_conditional_logic(dataset, group, result)

        # Validate overlay subtype semantic requirements
        OverlayPlaneValidator._validate_overlay_subtype_semantics(dataset, group, result)

    @staticmethod
    def _validate_type1_requirements(dataset: Dataset, group: int, result: ValidationResult) -> None:
        """Validate Type 1 (required) attributes for specific overlay group.

        Args:
            dataset: pydicom Dataset containing overlay elements
            group: Overlay group number (e.g., 0x6000)
            result: ValidationResult to accumulate errors
        """
        group_hex = f"{group:04X}"

        # Define required tags for this overlay group
        required_tags = [
            (Tag(group, 0x0010), f"Overlay Rows ({group_hex},0010)"),
            (Tag(group, 0x0011), f"Overlay Columns ({group_hex},0011)"),
            (Tag(group, 0x0040), f"Overlay Type ({group_hex},0040)"),
            (Tag(group, 0x0050), f"Overlay Origin ({group_hex},0050)"),
            (Tag(group, 0x0100), f"Overlay Bits Allocated ({group_hex},0100)"),
            (Tag(group, 0x0102), f"Overlay Bit Position ({group_hex},0102)"),
            (Tag(group, 0x3000), f"Overlay Data ({group_hex},3000)")
        ]

        # Check presence of required elements
        for tag, description in required_tags:
            if tag not in dataset:
                result.add_error(f"{description} is required (Type 1) for overlay group {group_hex}")

        # Validate specific Type 1 constraints
        rows_tag = Tag(group, 0x0010)
        if rows_tag in dataset:
            rows_value = dataset[rows_tag].value
            if not isinstance(rows_value, int) or rows_value < 1:
                result.add_error(
                    f"Overlay Rows ({group_hex},0010) must be a positive integer, "
                    f"got {rows_value} of type {type(rows_value).__name__}"
                )

        cols_tag = Tag(group, 0x0011)
        if cols_tag in dataset:
            cols_value = dataset[cols_tag].value
            if not isinstance(cols_value, int) or cols_value < 1:
                result.add_error(
                    f"Overlay Columns ({group_hex},0011) must be a positive integer, "
                    f"got {cols_value} of type {type(cols_value).__name__}"
                )

        origin_tag = Tag(group, 0x0050)
        if origin_tag in dataset:
            origin = dataset[origin_tag].value
            # Handle pydicom MultiValue objects as well as lists and tuples
            if not hasattr(origin, '__len__') or len(origin) != 2:
                result.add_error(
                    f"Overlay Origin ({group_hex},0050) must be a pair of values [row, column], "
                    f"got {origin}"
                )
            else:
                try:
                    row, col = int(origin[0]), int(origin[1])
                    # Note: DICOM allows values < 1 to indicate overlay extends beyond image
                except (ValueError, TypeError):
                    result.add_error(
                        f"Overlay Origin ({group_hex},0050) values must be integers, "
                        f"got [{origin[0]}, {origin[1]}]"
                    )

        bits_alloc_tag = Tag(group, 0x0100)
        if bits_alloc_tag in dataset:
            bits_value = dataset[bits_alloc_tag].value
            if bits_value != 1:
                result.add_error(
                    f"Overlay Bits Allocated ({group_hex},0100) must be 1 per DICOM standard, "
                    f"got {bits_value}. Embedding overlay data in pixel data is retired."
                )

        bit_pos_tag = Tag(group, 0x0102)
        if bit_pos_tag in dataset:
            pos_value = dataset[bit_pos_tag].value
            if pos_value != 0:
                result.add_error(
                    f"Overlay Bit Position ({group_hex},0102) must be 0 per DICOM standard, "
                    f"got {pos_value}. Embedding overlay data in pixel data is retired."
                )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, group: int, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM standard for specific overlay group.

        Args:
            dataset: pydicom Dataset containing overlay elements
            group: Overlay group number (e.g., 0x6000)
            result: ValidationResult to accumulate errors
        """
        group_hex = f"{group:04X}"

        # Overlay Type validation
        type_tag = Tag(group, 0x0040)
        if type_tag in dataset:
            overlay_type = dataset[type_tag].value
            valid_types = [e.value for e in OverlayType]
            if overlay_type not in valid_types:
                result.add_error(
                    f"Overlay Type ({group_hex},0040) must be one of {valid_types}, "
                    f"got '{overlay_type}'. Valid values: 'G' (Graphics) or 'R' (ROI)."
                )

        # Overlay Subtype validation
        subtype_tag = Tag(group, 0x0045)
        if subtype_tag in dataset:
            overlay_subtype = dataset[subtype_tag].value
            valid_subtypes = [e.value for e in OverlaySubtype]
            if overlay_subtype not in valid_subtypes:
                result.add_error(
                    f"Overlay Subtype ({group_hex},0045) must be one of {valid_subtypes}, "
                    f"got '{overlay_subtype}'. See DICOM PS3.3 C.9.2.1.3 for valid defined terms."
                )
    
    @staticmethod
    def _validate_overlay_data_consistency(dataset: Dataset, group: int, result: ValidationResult) -> None:
        """Validate overlay data consistency and size for specific overlay group.

        Args:
            dataset: pydicom Dataset containing overlay elements
            group: Overlay group number (e.g., 0x6000)
            result: ValidationResult to accumulate errors and warnings
        """
        group_hex = f"{group:04X}"

        rows_tag = Tag(group, 0x0010)
        cols_tag = Tag(group, 0x0011)
        data_tag = Tag(group, 0x3000)

        # Check if all required elements are present
        if not all(tag in dataset for tag in [rows_tag, cols_tag, data_tag]):
            return  # Type 1 validation will catch missing elements

        # Calculate expected data size
        rows = dataset[rows_tag].value
        cols = dataset[cols_tag].value
        total_pixels = rows * cols
        expected_bits = total_pixels
        expected_bytes = (expected_bits + 7) // 8  # Round up to nearest byte
        # DICOM requires even length for OW VR
        expected_bytes_padded = expected_bytes + (expected_bytes % 2)

        overlay_data = dataset[data_tag].value
        actual_bytes = len(overlay_data)

        if actual_bytes < expected_bytes:
            result.add_error(
                f"Overlay Data ({group_hex},3000) size ({actual_bytes} bytes) is less than required "
                f"for {rows}x{cols} overlay (minimum {expected_bytes} bytes). "
                f"Each overlay pixel requires 1 bit, packed into bytes."
            )
        elif actual_bytes != expected_bytes_padded:
            result.add_warning(
                f"Overlay Data ({group_hex},3000) size ({actual_bytes} bytes) differs from expected "
                f"padded size ({expected_bytes_padded} bytes) for {rows}x{cols} overlay. "
                f"DICOM OW VR requires even byte length."
            )

    @staticmethod
    def _validate_roi_statistics_conditional_logic(dataset: Dataset, group: int, result: ValidationResult) -> None:
        """Validate ROI statistics conditional logic per DICOM PS3.3 C.9.2.1.2.

        ROI statistics (ROI Area, ROI Mean, ROI Standard Deviation) are only meaningful
        when Overlay Type is 'R' (ROI). This implements the conditional logic from the
        DICOM standard.

        Args:
            dataset: pydicom Dataset containing overlay elements
            group: Overlay group number (e.g., 0x6000)
            result: ValidationResult to accumulate errors and warnings
        """
        group_hex = f"{group:04X}"

        type_tag = Tag(group, 0x0040)
        roi_area_tag = Tag(group, 0x1301)
        roi_mean_tag = Tag(group, 0x1302)
        roi_std_tag = Tag(group, 0x1303)

        roi_stats_tags = [roi_area_tag, roi_mean_tag, roi_std_tag]
        has_roi_stats = any(tag in dataset for tag in roi_stats_tags)

        # Check overlay type
        overlay_type = None
        if type_tag in dataset:
            overlay_type = dataset[type_tag].value

        # Validate conditional logic
        if has_roi_stats:
            if overlay_type != "R":
                result.add_warning(
                    f"ROI statistics are present in overlay group {group_hex} but Overlay Type "
                    f"({group_hex},0040) is '{overlay_type}', not 'R' (ROI). "
                    f"ROI statistics are only meaningful for ROI overlays per DICOM PS3.3 C.9.2.1.2."
                )

        # Validate individual ROI statistics if present
        if roi_area_tag in dataset:
            try:
                roi_area = int(dataset[roi_area_tag].value)
                if roi_area < 0:
                    result.add_error(
                        f"ROI Area ({group_hex},1301) must be non-negative, got {roi_area}"
                    )

                # Cross-validate with overlay size if available
                rows_tag = Tag(group, 0x0010)
                cols_tag = Tag(group, 0x0011)
                if rows_tag in dataset and cols_tag in dataset:
                    max_area = dataset[rows_tag].value * dataset[cols_tag].value
                    if roi_area > max_area:
                        result.add_warning(
                            f"ROI Area ({roi_area}) exceeds total overlay area ({max_area}) "
                            f"for overlay group {group_hex}"
                        )
            except (ValueError, TypeError):
                result.add_error(
                    f"ROI Area ({group_hex},1301) must be an integer, "
                    f"got {dataset[roi_area_tag].value}"
                )

        if roi_mean_tag in dataset:
            try:
                roi_mean = float(dataset[roi_mean_tag].value)
                # No specific constraints on ROI mean value per DICOM standard
            except (ValueError, TypeError):
                result.add_error(
                    f"ROI Mean ({group_hex},1302) must be numeric, "
                    f"got {dataset[roi_mean_tag].value}"
                )

        if roi_std_tag in dataset:
            try:
                roi_std = float(dataset[roi_std_tag].value)
                if roi_std < 0:
                    result.add_error(
                        f"ROI Standard Deviation ({group_hex},1303) must be non-negative, "
                        f"got {roi_std}"
                    )
            except (ValueError, TypeError):
                result.add_error(
                    f"ROI Standard Deviation ({group_hex},1303) must be numeric, "
                    f"got {dataset[roi_std_tag].value}"
                )

    @staticmethod
    def _validate_multi_frame_conditional_logic(dataset: Dataset, group: int, result: ValidationResult) -> None:
        """Validate multi-frame overlay conditional logic per DICOM PS3.3 C.9.2.1.4.

        When Number of Frames in Overlay (60xx,0015) and Image Frame Origin (60xx,0051)
        are absent, the overlay SHALL be applied to all frames in the multi-frame image.
        When present, they define frame-specific overlay behavior.

        Args:
            dataset: pydicom Dataset containing overlay elements
            group: Overlay group number (e.g., 0x6000)
            result: ValidationResult to accumulate errors and warnings
        """
        group_hex = f"{group:04X}"

        frames_tag = Tag(group, 0x0015)
        origin_tag = Tag(group, 0x0051)

        has_frames = frames_tag in dataset
        has_origin = origin_tag in dataset

        # Validate consistency of multi-frame elements
        if has_frames and not has_origin:
            result.add_warning(
                f"Number of Frames in Overlay ({group_hex},0015) is present but "
                f"Image Frame Origin ({group_hex},0051) is missing. "
                f"Both elements should be present together for multi-frame overlays."
            )
        elif has_origin and not has_frames:
            result.add_warning(
                f"Image Frame Origin ({group_hex},0051) is present but "
                f"Number of Frames in Overlay ({group_hex},0015) is missing. "
                f"Both elements should be present together for multi-frame overlays."
            )

        # Validate frame count if present
        if has_frames:
            try:
                frame_count = int(dataset[frames_tag].value)
                if frame_count < 1:
                    result.add_error(
                        f"Number of Frames in Overlay ({group_hex},0015) must be positive, "
                        f"got {frame_count}"
                    )
            except (ValueError, TypeError):
                result.add_error(
                    f"Number of Frames in Overlay ({group_hex},0015) must be an integer, "
                    f"got {dataset[frames_tag].value}"
                )

        # Validate frame origins if present
        if has_origin:
            try:
                frame_origins = dataset[origin_tag].value
                if not hasattr(frame_origins, '__len__'):
                    frame_origins = [frame_origins]  # Handle single value

                for i, origin in enumerate(frame_origins):
                    frame_num = int(origin)
                    if frame_num < 1:
                        result.add_error(
                            f"Image Frame Origin ({group_hex},0051) frame {i+1} must be >= 1 "
                            f"(1-based frame numbering), got {frame_num}"
                        )
            except (ValueError, TypeError):
                result.add_error(
                    f"Image Frame Origin ({group_hex},0051) must contain integer frame numbers, "
                    f"got {dataset[origin_tag].value}"
                )

    @staticmethod
    def _validate_overlay_subtype_semantics(dataset: Dataset, group: int, result: ValidationResult) -> None:
        """Validate overlay subtype semantic requirements per DICOM PS3.3 C.9.2.1.3.

        Different overlay subtypes have specific semantic requirements and use cases.
        This validates that the subtype is appropriate for the overlay type and context.

        Args:
            dataset: pydicom Dataset containing overlay elements
            group: Overlay group number (e.g., 0x6000)
            result: ValidationResult to accumulate errors and warnings
        """
        group_hex = f"{group:04X}"

        type_tag = Tag(group, 0x0040)
        subtype_tag = Tag(group, 0x0045)

        if subtype_tag not in dataset:
            return  # Subtype is Type 3 (optional)

        overlay_type = dataset[type_tag].value if type_tag in dataset else None
        overlay_subtype = dataset[subtype_tag].value

        # Validate ACTIVE_IMAGE_AREA subtype special requirements
        if overlay_subtype == OverlaySubtype.ACTIVE_IMAGE_AREA.value:
            if overlay_type != "R":
                result.add_warning(
                    f"Overlay Subtype 'ACTIVE_IMAGE_AREA' in group {group_hex} is typically "
                    f"used with ROI overlays (Type 'R'), but Overlay Type is '{overlay_type}'. "
                    f"Active image area overlays identify pixels generated from image data acquisition."
                )

            # Additional semantic validation for active image area
            result.add_info(
                f"Overlay group {group_hex} uses ACTIVE_IMAGE_AREA subtype. "
                f"This overlay should identify all pixels generated from image data acquisition "
                f"with overlay bit value of 1, excluding burned-in annotations per DICOM PS3.3 C.9.2.1.3."
            )


