"""
General Study Module - DICOM PS3.3 C.7.2.1

The General Study Module contains attributes that identify and describe
the Study performed upon the Patient.
"""
from datetime import datetime, date
from pydicom import Dataset
from pydicom.valuerep import PersonName
from .base_module import BaseModule
from ...utils.dicom_formatters import format_date_value, format_time_value
from ...validators.modules.base_validator import ValidationConfig
from ...validators.modules.general_study_validator import GeneralStudyValidator
from ...validators import ValidationResult


class GeneralStudyModule(BaseModule):
    """General Study Module implementation for DICOM PS3.3 C.7.2.1.

    Uses composition with internal dataset management rather than inheriting
    from pydicom.Dataset for cleaner separation of concerns.
    Contains attributes that identify and describe the Study performed upon the Patient.

    Usage:
        # Create with required elements
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid="*******.*******.9",
            study_date="20240101",
            study_time="120000",
            referring_physicians_name="<PERSON>^<PERSON>",
            study_id="STUDY001",
            accession_number="ACC123456"
        )

        # Add optional elements
        study.with_optional_elements(
            study_description="Chest CT with contrast",
            physicians_of_record="<PERSON>^<PERSON>"
        )

        # Add physician identification
        study.with_physician_identification(
            referring_physician_identification_sequence=[
                GeneralStudyModule.create_person_identification_item(
                    code_value="12345",
                    coding_scheme_designator="LOCAL",
                    code_meaning="Smith^John",
                    institution_name="General Hospital"
                )
            ]
        )

        # Generate dataset for IOD integration
        dataset = study.to_dataset()

        # Validate
        result = study.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        study_instance_uid: str,
        study_date: str | datetime | date = "",
        study_time: str | datetime = "",
        referring_physicians_name: str | PersonName = "",
        study_id: str = "",
        accession_number: str = ""
    ) -> 'GeneralStudyModule':
        """Create GeneralStudyModule from all required (Type 1 and Type 2) data elements.

        Args:
            study_instance_uid (str): Unique identifier for the Study (0020,000D) Type 1
            study_date (str | datetime | date): Date the Study started (0008,0020) Type 2
            study_time (str | datetime): Time the Study started (0008,0030) Type 2
            referring_physicians_name (str | PersonName): Name of referring physician (0008,0090) Type 2
            study_id (str): User or equipment generated Study identifier (0020,0010) Type 2
            accession_number (str): Departmental IS generated number (0008,0050) Type 2

        Returns:
            GeneralStudyModule: New module instance with required data elements set
        """
        instance = cls()
        instance._dataset.StudyInstanceUID = study_instance_uid
        instance._dataset.StudyDate = format_date_value(study_date)
        instance._dataset.StudyTime = format_time_value(study_time)
        instance._dataset.ReferringPhysicianName = referring_physicians_name
        instance._dataset.StudyID = study_id
        instance._dataset.AccessionNumber = accession_number
        return instance
    
    def with_optional_elements(
        self,
        issuer_of_accession_number_sequence: list[Dataset] | None = None,
        study_description: str | None = None,
        physicians_of_record: str | PersonName | None = None,
        name_of_physicians_reading_study: str | PersonName | None = None,
        requesting_service: str | None = None,
        requesting_service_code_sequence: list[Dataset] | None = None,
        referenced_study_sequence: list[Dataset] | None = None,
        procedure_code_sequence: list[Dataset] | None = None,
        reason_for_performed_procedure_code_sequence: list[Dataset] | None = None
    ) -> 'GeneralStudyModule':
        """Add optional (Type 3) data elements without conditional requirements.

        Args:
            issuer_of_accession_number_sequence (list[Dataset] | None): Assigning Authority for accession number (0008,0051) Type 3.
                Only a single Item is permitted in this Sequence.
            study_description (str | None): Institution-generated description of the Study (0008,1030) Type 3
            physicians_of_record (str | PersonName | None): Physicians responsible for overall Patient care (0008,1048) Type 3.
                Multiple values (1-n) are permitted using backslash separator.
            name_of_physicians_reading_study (str | PersonName | None): Physicians reading the Study (0008,1060) Type 3.
                Multiple values (1-n) are permitted using backslash separator.
            requesting_service (str | None): Institutional department where request initiated (0032,1033) Type 3
            requesting_service_code_sequence (list[Dataset] | None): Coded requesting service (0032,1034) Type 3.
                Only a single Item is permitted in this Sequence.
            referenced_study_sequence (list[Dataset] | None): Reference to a Study (0008,1110) Type 3.
                One or more Items are permitted in this Sequence.
            procedure_code_sequence (list[Dataset] | None): Type of procedure performed (0008,1032) Type 3.
                One or more Items are permitted in this Sequence.
            reason_for_performed_procedure_code_sequence (list[Dataset] | None): Coded reasons for procedure (0040,1012) Type 3.
                One or more Items are permitted in this Sequence.

        Returns:
            GeneralStudyModule: Self with optional elements added
        """
        if issuer_of_accession_number_sequence is not None:
            self._dataset.IssuerOfAccessionNumberSequence = issuer_of_accession_number_sequence
        if study_description is not None:
            self._dataset.StudyDescription = study_description
        if physicians_of_record is not None:
            self._dataset.PhysiciansOfRecord = physicians_of_record
        if name_of_physicians_reading_study is not None:
            self._dataset.NameOfPhysiciansReadingStudy = name_of_physicians_reading_study
        if requesting_service is not None:
            self._dataset.RequestingService = requesting_service
        if requesting_service_code_sequence is not None:
            self._dataset.RequestingServiceCodeSequence = requesting_service_code_sequence
        if referenced_study_sequence is not None:
            self._dataset.ReferencedStudySequence = referenced_study_sequence
        if procedure_code_sequence is not None:
            self._dataset.ProcedureCodeSequence = procedure_code_sequence
        if reason_for_performed_procedure_code_sequence is not None:
            self._dataset.ReasonForPerformedProcedureCodeSequence = reason_for_performed_procedure_code_sequence
        return self
    
    def with_physician_identification(
        self,
        referring_physician_identification_sequence: list[Dataset] | None = None,
        consulting_physician_name: str | PersonName | None = None,
        consulting_physician_identification_sequence: list[Dataset] | None = None,
        physicians_of_record_identification_sequence: list[Dataset] | None = None,
        physicians_reading_study_identification_sequence: list[Dataset] | None = None
    ) -> 'GeneralStudyModule':
        """Add physician identification information with correspondence validation.

        Args:
            referring_physician_identification_sequence (list[Dataset] | None): Referring physician ID (0008,0096) Type 3.
                Only a single Item is permitted in this Sequence.
            consulting_physician_name (str | PersonName | None): Consulting physician(s) (0008,009C) Type 3.
                Multiple values (1-n) are permitted using backslash separator.
            consulting_physician_identification_sequence (list[Dataset] | None): Consulting physician IDs (0008,009D) Type 3.
                One or more Items are permitted. If more than one Item, the number and order shall correspond
                to the Value of Consulting Physician Name (0008,009C), if present.
            physicians_of_record_identification_sequence (list[Dataset] | None): Physicians of record IDs (0008,1049) Type 3.
                One or more Items are permitted. If more than one Item, the number and order shall correspond
                to the Value of Physician(s) of Record (0008,1048), if present.
            physicians_reading_study_identification_sequence (list[Dataset] | None): Reading physicians IDs (0008,1062) Type 3.
                One or more Items are permitted. If more than one Item, the number and order shall correspond
                to the Value of Name of Physician(s) Reading Study (0008,1060), if present.

        Returns:
            GeneralStudyModule: Self with physician identification elements added
        """
        if referring_physician_identification_sequence is not None:
            self._dataset.ReferringPhysicianIdentificationSequence = referring_physician_identification_sequence
        if consulting_physician_name is not None:
            self._dataset.ConsultingPhysicianName = consulting_physician_name
        if consulting_physician_identification_sequence is not None:
            self._dataset.ConsultingPhysicianIdentificationSequence = consulting_physician_identification_sequence
        if physicians_of_record_identification_sequence is not None:
            self._dataset.PhysiciansOfRecordIdentificationSequence = physicians_of_record_identification_sequence
        if physicians_reading_study_identification_sequence is not None:
            self._dataset.PhysiciansReadingStudyIdentificationSequence = physicians_reading_study_identification_sequence
        return self
    
    @staticmethod
    def create_referenced_study_item(
        study_instance_uid: str,
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str
    ) -> Dataset:
        """Create an item for Referenced Study Sequence (0008,1110).
        
        Args:
            study_instance_uid (str): Instance UID of referenced Study
            referenced_sop_class_uid (str): SOP Class UID of referenced object
            referenced_sop_instance_uid (str): SOP Instance UID of referenced object
            
        Returns:
            Dataset: Sequence item with study reference information
        """
        item = Dataset()
        item.StudyInstanceUID = study_instance_uid
        item.ReferencedSOPClassUID = referenced_sop_class_uid
        item.ReferencedSOPInstanceUID = referenced_sop_instance_uid
        return item
    
    @staticmethod
    def create_person_identification_item(
        code_value: str,
        coding_scheme_designator: str,
        code_meaning: str,
        institution_name: str | None = None,
        institution_code_sequence: list[Dataset] | None = None,
        persons_address: str | None = None,
        persons_telephone_numbers: str | None = None,
        persons_telecom_information: str | None = None,
        institution_address: str | None = None,
        coding_scheme_version: str | None = None,
        long_code_value: str | None = None,
        urn_code_value: str | None = None
    ) -> Dataset:
        """Create an item for person identification sequences (Table 10-1 Person Identification Macro).

        Implements DICOM PS3.3 Table 10-1 Person Identification Macro Attributes.

        Args:
            code_value (str): Code value for person identification (0008,0100) Type 1C
            coding_scheme_designator (str): Coding scheme designator (0008,0102) Type 1C
            code_meaning (str): Code meaning (0008,0104) Type 1
            institution_name (str | None): Institution name (0008,0080) Type 1C - Required if institution_code_sequence not present
            institution_code_sequence (list[dict] | None): Institution code sequence (0008,0082) Type 1C - Required if institution_name not present
            persons_address (str | None): Person's mailing address (0040,1102) Type 3
            persons_telephone_numbers (str | None): Person's telephone numbers (0040,1103) Type 3
            persons_telecom_information (str | None): Person's telecom information (0040,1104) Type 3
            institution_address (str | None): Institution address (0008,0081) Type 3
            coding_scheme_version (str | None): Coding scheme version (0008,0103) Type 1C
            long_code_value (str | None): Long code value (0008,0119) Type 1C
            urn_code_value (str | None): URN code value (0008,0120) Type 1C

        Returns:
            Dataset: Sequence item with person identification information per Table 10-1

        Raises:
            ValueError: If neither institution_name nor institution_code_sequence is provided
        """
        # Validate Type 1C requirements
        if not institution_name and not institution_code_sequence:
            raise ValueError("Either institution_name or institution_code_sequence must be provided (Type 1C requirement)")

        # Create Person Identification Code Sequence item (Table 8.8-1 Code Sequence Macro)
        person_id_code_item = Dataset()
        person_id_code_item.CodeMeaning = code_meaning

        # Handle Code Value variants (Type 1C - at least one must be present)
        if code_value:
            person_id_code_item.CodeValue = code_value
            person_id_code_item.CodingSchemeDesignator = coding_scheme_designator
        elif long_code_value:
            person_id_code_item.LongCodeValue = long_code_value
            person_id_code_item.CodingSchemeDesignator = coding_scheme_designator
        elif urn_code_value:
            person_id_code_item.URNCodeValue = urn_code_value
        else:
            raise ValueError("At least one of code_value, long_code_value, or urn_code_value must be provided")

        # Add optional coding scheme version
        if coding_scheme_version:
            person_id_code_item.CodingSchemeVersion = coding_scheme_version

        # Build the main item according to Table 10-1
        item = Dataset()
        item.PersonIdentificationCodeSequence = [person_id_code_item]

        # Add optional Type 3 attributes
        if persons_address:
            item.PersonsAddress = persons_address
        if persons_telephone_numbers:
            item.PersonsTelephoneNumbers = persons_telephone_numbers
        if persons_telecom_information:
            item.PersonsTelecomInformation = persons_telecom_information
        if institution_address:
            item.InstitutionAddress = institution_address

        # Add Type 1C institution information (exactly one must be present)
        if institution_name:
            item.InstitutionName = institution_name
        if institution_code_sequence:
            item.InstitutionCodeSequence = institution_code_sequence

        return item

    @property
    def has_physician_identification(self) -> bool:
        """Check if physician identification information is present.

        Returns:
            bool: True if any physician identification sequences are present
        """
        return any(hasattr(self._dataset, attr) for attr in [
            'ReferringPhysicianIdentificationSequence',
            'ConsultingPhysicianIdentificationSequence',
            'PhysiciansOfRecordIdentificationSequence',
            'PhysiciansReadingStudyIdentificationSequence'
        ])

    @property
    def has_procedure_info(self) -> bool:
        """Check if procedure information is present.

        Returns:
            bool: True if procedure-related elements are present
        """
        return any(hasattr(self._dataset, attr) for attr in [
            'ProcedureCodeSequence',
            'ReasonForPerformedProcedureCodeSequence'
        ])

    @property
    def has_requesting_service_info(self) -> bool:
        """Check if requesting service information is present.

        Returns:
            bool: True if requesting service elements are present
        """
        return (hasattr(self._dataset, 'RequestingService') or
                hasattr(self._dataset, 'RequestingServiceCodeSequence'))
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this General Study Module instance.

        Args:
            config (ValidationConfig | None): Optional validation configuration

        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return GeneralStudyValidator.validate(self._dataset, config)
