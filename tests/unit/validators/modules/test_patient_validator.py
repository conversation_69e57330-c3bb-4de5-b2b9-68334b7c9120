"""
Test PatientValidator functionality for DICOM PS3.3 C.7.1.1 Patient Module.

Comprehensive unit tests validating all Type 1, Type 2, Type 3, Type 1C, and Type 2C
requirements including complex conditional logic, enumerated values, and sequence validation.
All tests use datasets generated from modules via to_dataset() method per the
composition-based architecture requirements.
"""
import pytest
import pydicom
from pydicom import Dataset
from pyrt_dicom.modules import PatientModule
from pyrt_dicom.validators.modules.patient_validator import PatientValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums import PatientSex, ResponsiblePersonRole, TypeOfPatientID


class TestPatientValidator:
    """Test PatientValidator comprehensive validation functionality."""
    
    def create_valid_patient_dataset(self) -> Dataset:
        """Create a valid basic patient module dataset for testing."""
        patient = PatientModule.from_required_elements(
            patient_name="<PERSON><PERSON>^John",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE
        )
        return patient.to_dataset()
    
    def test_validate_method_signature(self):
        """Test that validate method has correct signature and returns ValidationResult."""
        dataset = self.create_valid_patient_dataset()
        config = ValidationConfig()
        
        # Test method exists and is callable
        assert hasattr(PatientValidator, 'validate')
        assert callable(PatientValidator.validate)
        
        # Test it accepts dataset and config parameters
        result = PatientValidator.validate(dataset, config)
        
        # Test it returns ValidationResult
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_validate_with_none_config(self):
        """Test validation works with None config (uses default)."""
        dataset = self.create_valid_patient_dataset()
        
        result = PatientValidator.validate(dataset, None)
        
        assert isinstance(result, ValidationResult)
        # Should not have errors for valid dataset
        assert len(result.errors) == 0
    
    def test_valid_dataset_passes_validation(self):
        """Test that valid patient dataset passes validation without errors."""
        dataset = self.create_valid_patient_dataset()
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    # Type 2 Element Tests
    def test_missing_type_2_patient_name(self):
        """Test validation fails when Type 2 Patient's Name is missing."""
        dataset = self.create_valid_patient_dataset()
        delattr(dataset, 'PatientName')
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "Patient's Name (0010,0010)" in error_msg
        assert "Type 2 element" in error_msg
        assert "required" in error_msg
    
    def test_missing_type_2_patient_id(self):
        """Test validation fails when Type 2 Patient ID is missing."""
        dataset = self.create_valid_patient_dataset()
        delattr(dataset, 'PatientID')
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "Patient ID (0010,0020)" in error_msg
        assert "Type 2 element" in error_msg
    
    def test_missing_type_2_patient_birth_date(self):
        """Test validation fails when Type 2 Patient's Birth Date is missing."""
        dataset = self.create_valid_patient_dataset()
        delattr(dataset, 'PatientBirthDate')
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "Patient's Birth Date (0010,0030)" in error_msg
        assert "Type 2 element" in error_msg
    
    def test_missing_type_2_patient_sex(self):
        """Test validation fails when Type 2 Patient's Sex is missing."""
        dataset = self.create_valid_patient_dataset()
        delattr(dataset, 'PatientSex')
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "Patient's Sex (0010,0040)" in error_msg
        assert "Type 2 element" in error_msg
    
    def test_empty_type_2_elements_allowed(self):
        """Test that empty Type 2 elements are allowed (but must be present)."""
        patient = PatientModule.from_required_elements(
            patient_name="",  # Empty but present
            patient_id="",  # Empty but present
            patient_birth_date="",  # Empty but present
            patient_sex=""  # Empty but present
        )
        dataset = patient.to_dataset()
        
        result = PatientValidator.validate(dataset)
        
        # Should not have Type 2 errors, only potential enum warnings
        type_2_errors = [e for e in result.errors if "Type 2 element" in str(e)]
        assert len(type_2_errors) == 0
    
    # Enumerated Value Tests
    def test_invalid_patient_sex_generates_warning(self):
        """Test invalid patient sex generates warning."""
        dataset = self.create_valid_patient_dataset()
        dataset.PatientSex = "INVALID"
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.warnings) > 0
        warning_msg = str(result.warnings[0])
        assert "Patient's Sex (0010,0040)" in warning_msg
        assert "INVALID" in warning_msg
    
    def test_valid_patient_sex_values(self):
        """Test all valid patient sex enum values pass validation."""
        valid_values = ["M", "F", "O"]
        
        for sex_value in valid_values:
            dataset = self.create_valid_patient_dataset()
            dataset.PatientSex = sex_value
            
            result = PatientValidator.validate(dataset)
            
            # Should not have warnings for valid enum values
            sex_warnings = [w for w in result.warnings if "Patient's Sex" in str(w)]
            assert len(sex_warnings) == 0
    
    def test_invalid_quality_control_subject_generates_warning(self):
        """Test invalid quality control subject generates warning."""
        dataset = self.create_valid_patient_dataset()
        dataset.QualityControlSubject = "INVALID"
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.warnings) > 0
        warning_msg = str(result.warnings[0])
        assert "Quality Control Subject (0010,0200)" in warning_msg
    
    def test_valid_quality_control_subject_values(self):
        """Test valid quality control subject values pass validation."""
        valid_values = ["YES", "NO"]
        
        for qc_value in valid_values:
            dataset = self.create_valid_patient_dataset()
            dataset.QualityControlSubject = qc_value
            
            result = PatientValidator.validate(dataset)
            
            qc_warnings = [w for w in result.warnings if "Quality Control Subject" in str(w)]
            assert len(qc_warnings) == 0
    
    def test_invalid_type_of_patient_id_generates_warning(self):
        """Test invalid type of patient ID generates warning."""
        dataset = self.create_valid_patient_dataset()
        dataset.TypeOfPatientID = "INVALID"
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.warnings) > 0
        warning_msg = str(result.warnings[0])
        assert "Type of Patient ID (0010,0022)" in warning_msg
    
    def test_valid_type_of_patient_id_values(self):
        """Test valid type of patient ID values pass validation."""
        valid_values = ["TEXT", "RFID", "BARCODE"]
        
        for id_type in valid_values:
            dataset = self.create_valid_patient_dataset()
            dataset.TypeOfPatientID = id_type
            
            result = PatientValidator.validate(dataset)
            
            id_warnings = [w for w in result.warnings if "Type of Patient ID" in str(w)]
            assert len(id_warnings) == 0
    
    def test_invalid_patient_identity_removed_generates_warning(self):
        """Test invalid patient identity removed generates warning."""
        dataset = self.create_valid_patient_dataset()
        dataset.PatientIdentityRemoved = "INVALID"
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.warnings) > 0
        warning_msg = str(result.warnings[0])
        assert "Patient Identity Removed (0012,0062)" in warning_msg
    
    def test_responsible_person_role_enumerated_values(self):
        """Test responsible person role enumerated value validation."""
        dataset = self.create_valid_patient_dataset()
        dataset.ResponsiblePerson = "Dr. Smith"
        dataset.ResponsiblePersonRole = "INVALID"
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.warnings) > 0
        warning_msg = str(result.warnings[0])
        assert "Responsible Person Role (0010,2298)" in warning_msg
        
        # Test valid role
        dataset.ResponsiblePersonRole = ResponsiblePersonRole.VETERINARIAN.value
        result = PatientValidator.validate(dataset)
        role_warnings = [w for w in result.warnings if "Responsible Person Role" in str(w)]
        assert len(role_warnings) == 0
    
    # Type 1C Conditional Logic Tests
    def test_alternative_calendar_type_1c_requirement(self):
        """Test Type 1C requirement for alternative calendar."""
        dataset = self.create_valid_patient_dataset()
        dataset.PatientBirthDateInAlternativeCalendar = "5750/10/01"
        # Missing required PatientAlternativeCalendar
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "Patient's Alternative Calendar (0010,0035)" in error_msg
        assert "Type 1C requirement" in error_msg
        assert "required when" in error_msg
    
    def test_alternative_calendar_with_death_date_type_1c(self):
        """Test Type 1C requirement triggered by death date in alternative calendar."""
        dataset = self.create_valid_patient_dataset()
        dataset.PatientDeathDateInAlternativeCalendar = "5781/03/15"
        # Missing required PatientAlternativeCalendar
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "Patient's Alternative Calendar (0010,0035)" in error_msg
        assert "Type 1C requirement" in error_msg
    
    def test_alternative_calendar_requirement_satisfied(self):
        """Test alternative calendar requirement satisfied when calendar provided."""
        dataset = self.create_valid_patient_dataset()
        dataset.PatientBirthDateInAlternativeCalendar = "5750/10/01"
        dataset.PatientAlternativeCalendar = "HEBREW"
        
        result = PatientValidator.validate(dataset)
        
        # Should not have Type 1C errors for alternative calendar
        calendar_errors = [e for e in result.errors if "Alternative Calendar" in str(e)]
        assert len(calendar_errors) == 0
    
    def test_responsible_person_role_type_1c_requirement(self):
        """Test Type 1C requirement for responsible person role."""
        dataset = self.create_valid_patient_dataset()
        dataset.ResponsiblePerson = "Dr. Smith"
        # Missing required ResponsiblePersonRole
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "Responsible Person Role (0010,2298)" in error_msg
        assert "Type 1C requirement" in error_msg
        assert "required when" in error_msg
    
    def test_responsible_person_role_requirement_satisfied(self):
        """Test responsible person role requirement satisfied."""
        dataset = self.create_valid_patient_dataset()
        dataset.ResponsiblePerson = "Dr. Smith"
        dataset.ResponsiblePersonRole = ResponsiblePersonRole.VETERINARIAN.value
        
        result = PatientValidator.validate(dataset)
        
        # Should not have Type 1C errors for responsible person role
        role_errors = [e for e in result.errors if "Responsible Person Role" in str(e)]
        assert len(role_errors) == 0
    
    def test_deidentification_method_type_1c_requirement(self):
        """Test Type 1C requirement for deidentification method."""
        dataset = self.create_valid_patient_dataset()
        dataset.PatientIdentityRemoved = "YES"
        # Missing required method or method code sequence
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "De-identification Method" in error_msg
        assert "De-identification Method Code Sequence" in error_msg
        assert "Type 1C requirement" in error_msg
    
    def test_deidentification_method_requirement_satisfied_with_method(self):
        """Test deidentification requirement satisfied with method."""
        dataset = self.create_valid_patient_dataset()
        dataset.PatientIdentityRemoved = "YES"
        dataset.DeIdentificationMethod = "HIPAA Safe Harbor"
        
        result = PatientValidator.validate(dataset)
        
        # Should not have Type 1C errors for deidentification
        deidentification_errors = [e for e in result.errors if "De-identification Method" in str(e)]
        assert len(deidentification_errors) == 0
    
    def test_deidentification_method_requirement_satisfied_with_code_sequence(self):
        """Test deidentification requirement satisfied with code sequence."""
        dataset = self.create_valid_patient_dataset()
        dataset.PatientIdentityRemoved = "YES"
        dataset.DeIdentificationMethodCodeSequence = [pydicom.Dataset()]
        
        result = PatientValidator.validate(dataset)
        
        # Should not have Type 1C errors for deidentification
        deidentification_errors = [e for e in result.errors if "De-identification Method" in str(e)]
        assert len(deidentification_errors) == 0
    
    # Non-human Organism Type 1C/2C Tests
    def test_non_human_species_type_1c_requirement(self):
        """Test Type 1C requirement for non-human organism species."""
        patient = PatientModule.from_required_elements(
            patient_name="Lab^Rat^001",
            patient_id="RAT001",
            patient_birth_date="20230101",
            patient_sex=PatientSex.MALE
        ).with_non_human_organism(
            # This should trigger the validator error checking since no species provided
            responsible_person="Dr. Smith"
        )
        
        # Manually remove species info to test validation
        dataset = patient.to_dataset()
        if hasattr(dataset, 'PatientSpeciesDescription'):
            delattr(dataset, 'PatientSpeciesDescription')
        if hasattr(dataset, 'PatientSpeciesCodeSequence'):
            delattr(dataset, 'PatientSpeciesCodeSequence')
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "Patient Species Description (0010,2201)" in error_msg
        assert "Patient Species Code Sequence (0010,2202)" in error_msg
        assert "Type 1C requirement" in error_msg
    
    def test_non_human_species_requirement_satisfied_with_description(self):
        """Test non-human species requirement satisfied with description."""
        patient = PatientModule.from_required_elements(
            patient_name="Lab^Rat^001",
            patient_id="RAT001", 
            patient_birth_date="20230101",
            patient_sex=PatientSex.MALE
        ).with_non_human_organism(
            patient_species_description="Rattus rattus",
            responsible_person="Dr. Smith"
        )
        dataset = patient.to_dataset()
        
        result = PatientValidator.validate(dataset)
        
        # Should not have Type 1C errors for species
        species_errors = [e for e in result.errors if "Patient Species" in str(e)]
        assert len(species_errors) == 0
    
    def test_non_human_type_2c_breed_code_sequence_required(self):
        """Test Type 2C requirement for breed code sequence in non-human organisms."""
        dataset = self.create_valid_patient_dataset()
        dataset.PatientSpeciesDescription = "Canis lupus familiaris"
        # Missing required PatientBreedCodeSequence for non-human
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "Patient Breed Code Sequence (0010,2293)" in error_msg
        assert "Type 2C requirement" in error_msg
        assert "non-human organisms" in error_msg
    
    def test_non_human_type_2c_breed_description_when_code_empty(self):
        """Test Type 2C requirement for breed description when code sequence is empty."""
        dataset = self.create_valid_patient_dataset()
        dataset.PatientSpeciesDescription = "Canis lupus familiaris"
        dataset.PatientBreedCodeSequence = []  # Empty sequence
        # Missing breed description when sequence is empty
        
        result = PatientValidator.validate(dataset)
        
        # Should have error for missing breed description
        breed_desc_errors = [e for e in result.errors if "Patient Breed Description (0010,2292)" in str(e)]
        assert len(breed_desc_errors) > 0
        error_msg = str(breed_desc_errors[0])
        assert "Type 2C requirement" in error_msg
        assert "empty" in error_msg
    
    def test_non_human_type_2c_responsible_person_required(self):
        """Test Type 2C requirement for responsible person in non-human organisms."""
        dataset = self.create_valid_patient_dataset()
        dataset.PatientSpeciesDescription = "Canis lupus familiaris"
        # Missing required ResponsiblePerson for non-human
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "Responsible Person (0010,2297)" in error_msg
        assert "Type 2C requirement" in error_msg
        assert "non-human organisms" in error_msg
    
    def test_non_human_type_2c_responsible_organization_required(self):
        """Test Type 2C requirement for responsible organization in non-human organisms."""
        dataset = self.create_valid_patient_dataset()
        dataset.PatientSpeciesDescription = "Canis lupus familiaris"
        # Missing required ResponsibleOrganization for non-human
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "Responsible Organization (0010,2299)" in error_msg
        assert "Type 2C requirement" in error_msg
        assert "non-human organisms" in error_msg
    
    # Sequence Validation Tests
    def test_other_patient_ids_sequence_validation(self):
        """Test validation of Other Patient IDs Sequence items."""
        dataset = self.create_valid_patient_dataset()
        # Create invalid sequence item missing required elements
        invalid_item = pydicom.Dataset()
        # Missing PatientID and TypeOfPatientID
        dataset.OtherPatientIDsSequence = [invalid_item]
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.errors) >= 2  # Should have errors for both missing elements
        error_messages = [str(e) for e in result.errors]
        patient_id_errors = [e for e in error_messages if "Patient ID (0010,0020) is required" in e]
        type_id_errors = [e for e in error_messages if "Type of Patient ID (0010,0022) is required" in e]
        
        assert len(patient_id_errors) > 0
        assert len(type_id_errors) > 0
    
    def test_strain_stock_sequence_validation(self):
        """Test validation of Strain Stock Sequence items."""
        dataset = self.create_valid_patient_dataset()
        # Create invalid sequence item missing required elements
        invalid_item = pydicom.Dataset()
        # Missing required elements
        dataset.StrainStockSequence = [invalid_item]
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.errors) >= 3  # Should have errors for missing elements
        error_messages = [str(e) for e in result.errors]
        stock_num_errors = [e for e in error_messages if "Strain Stock Number (0010,0214) is required" in e]
        source_errors = [e for e in error_messages if "Strain Source (0010,0217) is required" in e]
        registry_errors = [e for e in error_messages if "Strain Source Registry Code Sequence (0010,0215) is required" in e]
        
        assert len(stock_num_errors) > 0
        assert len(source_errors) > 0
        assert len(registry_errors) > 0
    
    def test_genetic_modifications_sequence_validation(self):
        """Test validation of Genetic Modifications Sequence items."""
        dataset = self.create_valid_patient_dataset()
        # Create invalid sequence item missing required elements
        invalid_item = pydicom.Dataset()
        # Missing required elements
        dataset.GeneticModificationsSequence = [invalid_item]
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.errors) >= 2  # Should have errors for missing elements
        error_messages = [str(e) for e in result.errors]
        desc_errors = [e for e in error_messages if "Genetic Modifications Description (0010,0222) is required" in e]
        nom_errors = [e for e in error_messages if "Genetic Modifications Nomenclature (0010,0223) is required" in e]
        
        assert len(desc_errors) > 0
        assert len(nom_errors) > 0
    
    # ValidationConfig Tests
    def test_validation_config_disable_conditional_requirements(self):
        """Test that disabling conditional requirements skips Type 1C/2C validation."""
        dataset = self.create_valid_patient_dataset()
        dataset.PatientBirthDateInAlternativeCalendar = "5750/10/01"
        # Missing required PatientAlternativeCalendar, but disable conditional validation
        
        config = ValidationConfig()
        config.validate_conditional_requirements = False
        
        result = PatientValidator.validate(dataset, config)
        
        # Should not have Type 1C errors when conditional validation is disabled
        calendar_errors = [e for e in result.errors if "Alternative Calendar" in str(e)]
        assert len(calendar_errors) == 0
    
    def test_validation_config_disable_enumerated_values(self):
        """Test that disabling enumerated value checks skips enum validation."""
        dataset = self.create_valid_patient_dataset()
        dataset.PatientSex = "INVALID"
        
        config = ValidationConfig()
        config.check_enumerated_values = False
        
        result = PatientValidator.validate(dataset, config)
        
        # Should not have enum warnings when enum validation is disabled
        sex_warnings = [w for w in result.warnings if "Patient's Sex" in str(w)]
        assert len(sex_warnings) == 0
    
    def test_validation_config_disable_sequence_validation(self):
        """Test that disabling sequence validation skips sequence checks."""
        dataset = self.create_valid_patient_dataset()
        invalid_item = pydicom.Dataset()  # Missing required elements
        dataset.OtherPatientIDsSequence = [invalid_item]
        
        config = ValidationConfig()
        config.validate_sequences = False
        
        result = PatientValidator.validate(dataset, config)
        
        # Should not have sequence errors when sequence validation is disabled
        sequence_errors = [e for e in result.errors if "Patient ID (0010,0020) is required" in str(e)]
        assert len(sequence_errors) == 0
    
    # Error Message Quality Tests
    def test_error_messages_contain_dicom_tags(self):
        """Test that all error messages contain specific DICOM tag references."""
        dataset = Dataset()  # Empty dataset to trigger multiple errors
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.errors) > 0
        for error in result.errors:
            error_msg = str(error)
            # Each error should contain a DICOM tag reference in parentheses
            assert "(" in error_msg and ")" in error_msg
            # Should contain a tag pattern like (0010,0010)
            import re
            tag_pattern = r'\(\d{4},\d{4}\)'
            assert re.search(tag_pattern, error_msg) is not None
    
    def test_error_messages_contain_guidance(self):
        """Test that error messages contain actionable guidance."""
        dataset = Dataset()  # Empty dataset to trigger multiple errors
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.errors) > 0
        for error in result.errors:
            error_msg = str(error)
            # Should contain guidance keywords
            guidance_keywords = ["required", "See DICOM PS3.3", "Type", "requirement"]
            assert any(keyword in error_msg for keyword in guidance_keywords)
    
    def test_error_messages_reference_dicom_standard(self):
        """Test that error messages reference DICOM standard sections."""
        dataset = self.create_valid_patient_dataset()
        dataset.PatientBirthDateInAlternativeCalendar = "5750/10/01"
        # Missing required PatientAlternativeCalendar
        
        result = PatientValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "DICOM PS3.3" in error_msg
        assert "C.7.1.1" in error_msg
    
    # Real-World Scenario Tests
    def test_complete_human_patient_validation(self):
        """Test validation of complete human patient with all optional elements."""
        patient = PatientModule.from_required_elements(
            patient_name="Smith^John^Michael^^MD",
            patient_id="MRN123456",
            patient_birth_date="19750315",
            patient_sex=PatientSex.MALE
        ).with_optional_elements(
            type_of_patient_id=TypeOfPatientID.TEXT,
            quality_control_subject="NO",
            patient_comments="Test patient for cardiac imaging",
            patient_birth_time="143000"
        )
        dataset = patient.to_dataset()
        
        result = PatientValidator.validate(dataset)
        
        # Should pass all validation without errors or warnings
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_complete_non_human_patient_validation(self):
        """Test validation of complete non-human patient with all required elements."""
        patient = PatientModule.from_required_elements(
            patient_name="Lab^Rat^001",
            patient_id="RAT001",
            patient_birth_date="20230101", 
            patient_sex=PatientSex.MALE
        ).with_non_human_organism(
            patient_species_description="Rattus rattus",
            patient_breed_description="Sprague Dawley",
            responsible_person="Dr. Smith"
        ).with_responsible_person(
            responsible_person="Dr. Smith",
            responsible_person_role=ResponsiblePersonRole.VETERINARIAN
        )
        dataset = patient.to_dataset()
        
        result = PatientValidator.validate(dataset)
        
        # Should pass all validation without errors or warnings
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_deidentified_patient_validation(self):
        """Test validation of properly deidentified patient."""
        patient = PatientModule.from_required_elements(
            patient_name="",  # Deidentified
            patient_id="ANON001",
            patient_birth_date="",  # Deidentified
            patient_sex=PatientSex.OTHER
        ).with_deidentification(
            patient_identity_removed="YES",
            de_identification_method="HIPAA Safe Harbor"
        )
        dataset = patient.to_dataset()
        
        result = PatientValidator.validate(dataset)
        
        # Should pass all validation without errors or warnings
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    # Edge Case Tests
    def test_multiple_validation_errors_captured(self):
        """Test that multiple validation errors are all captured."""
        dataset = Dataset()  # Empty dataset
        # Add some invalid values
        dataset.PatientSex = "INVALID"
        dataset.QualityControlSubject = "INVALID"
        dataset.PatientIdentityRemoved = "YES"  # Missing required deidentification method
        
        result = PatientValidator.validate(dataset)
        
        # Should capture multiple different types of errors and warnings
        assert len(result.errors) >= 3  # Type 2 missing elements + deidentification
        assert len(result.warnings) >= 2  # Invalid enum values
    
    def test_validation_result_consistency(self):
        """Test that ValidationResult structure is consistent across scenarios."""
        test_datasets = [
            self.create_valid_patient_dataset(),  # Valid
            Dataset()  # Invalid
        ]
        
        for dataset in test_datasets:
            result = PatientValidator.validate(dataset)
            
            # Verify ValidationResult structure consistency
            assert isinstance(result, ValidationResult)
            assert hasattr(result, 'errors')
            assert hasattr(result, 'warnings')
            assert isinstance(result.errors, list)
            assert isinstance(result.warnings, list)
            
            # All error and warning items should be convertible to strings
            for error in result.errors:
                assert isinstance(str(error), str)
                assert len(str(error)) > 0
            
            for warning in result.warnings:
                assert isinstance(str(warning), str)
                assert len(str(warning)) > 0