"""
Test ROI Contour Validator functionality.

Tests ROIContourValidator according to DICOM PS3.3 C.8.8.6 ROI Contour Module
requirements. Validates proper error detection, conditional logic, and ValidationResult
structure using dataset-based validation patterns.
"""

import pytest
import pydicom
from pydicom import Dataset
from pyrt_dicom.validators.modules.roi_contour_validator import ROIContourValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.modules.modules.roi_contour_module import ROIContourModule
from pyrt_dicom.enums.rt_enums import ContourGeometricType


class TestROIContourValidator:
    """Test ROIContourValidator functionality."""
    
    def create_valid_dataset(self) -> Dataset:
        """Create a valid ROI contour dataset for testing."""
        module = ROIContourModule.from_required_elements(
            roi_contour_sequence=[
                ROIContourModule.create_roi_contour_item(
                    referenced_roi_number=1,
                    roi_display_color=[255, 0, 0],
                    contour_sequence=[
                        ROIContourModule.create_contour_item(
                            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
                            number_of_contour_points=4,
                            contour_data=[10.0, 10.0, 0.0, 20.0, 10.0, 0.0, 20.0, 20.0, 0.0, 10.0, 20.0, 0.0]
                        )
                    ]
                )
            ]
        )
        return module.to_dataset()
    
    def test_validate_valid_data(self):
        """Test that valid ROI contour data passes validation without errors."""
        dataset = self.create_valid_dataset()
        
        result = ROIContourValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert isinstance(result.warnings, list)
    
    def test_validate_missing_roi_contour_sequence(self):
        """Test validation failure for missing ROI Contour Sequence."""
        dataset = Dataset()
        
        result = ROIContourValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) > 0
        assert any("ROI Contour Sequence (3006,0039) is required" in error for error in result.errors)
    
    def test_validate_missing_referenced_roi_number(self):
        """Test validation failure for missing Referenced ROI Number."""
        dataset = Dataset()
        roi_item = Dataset()
        roi_item.ROIDisplayColor = [255, 0, 0]
        # Missing ReferencedROINumber
        dataset.ROIContourSequence = [roi_item]
        
        result = ROIContourValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) > 0
        assert any("Referenced ROI Number (3006,0084) is required" in error for error in result.errors)
    
    def test_validate_missing_roi_display_color(self):
        """Test validation failure for missing ROI Display Color."""
        dataset = Dataset()
        roi_item = Dataset()
        roi_item.ReferencedROINumber = 1
        # Missing ROIDisplayColor
        dataset.ROIContourSequence = [roi_item]
        
        result = ROIContourValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) > 0
        assert any("ROI Display Color (3006,002A) is required" in error for error in result.errors)
    
    def test_validate_invalid_roi_display_color_length(self):
        """Test validation failure for invalid ROI Display Color length."""
        dataset = Dataset()
        roi_item = Dataset()
        roi_item.ReferencedROINumber = 1
        roi_item.ROIDisplayColor = [255, 0]  # Only 2 values instead of 3
        dataset.ROIContourSequence = [roi_item]
        
        result = ROIContourValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) > 0
        assert any(
            "ROI Display Color (3006,002A) must be RGB triplet (3 values)" in error 
            for error in result.errors
        )
    
    def test_validate_roi_display_color_range_warning(self):
        """Test validation warning for ROI Display Color values outside 0-255 range."""
        dataset = Dataset()
        roi_item = Dataset()
        roi_item.ReferencedROINumber = 1
        roi_item.ROIDisplayColor = [300, -10, 128]  # Values outside 0-255 range
        dataset.ROIContourSequence = [roi_item]
        
        result = ROIContourValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.warnings) > 0
        assert any(
            "ROI Display Color values should be in range 0-255" in warning 
            for warning in result.warnings
        )
    
    def test_validate_invalid_contour_geometric_type(self):
        """Test validation warning for invalid Contour Geometric Type."""
        dataset = self.create_valid_dataset()
        
        # Modify to have invalid geometric type
        contour_item = dataset.ROIContourSequence[0].ContourSequence[0]
        contour_item.ContourGeometricType = "INVALID_TYPE"
        
        config = ValidationConfig(check_enumerated_values=True)
        result = ROIContourValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.warnings) > 0
        assert any("Contour Geometric Type" in warning for warning in result.warnings)
    
    def test_validate_missing_contour_required_fields(self):
        """Test validation failure for missing required contour fields."""
        dataset = Dataset()
        roi_item = Dataset()
        roi_item.ReferencedROINumber = 1
        roi_item.ROIDisplayColor = [255, 0, 0]
        
        contour_item = Dataset()
        # Missing required fields: ContourGeometricType, NumberOfContourPoints, ContourData
        roi_item.ContourSequence = [contour_item]
        dataset.ROIContourSequence = [roi_item]
        
        result = ROIContourValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) >= 3  # Should have errors for all missing required fields
        
        error_text = ' '.join(result.errors)
        assert "Contour Geometric Type (3006,0042) is required" in error_text
        assert "Number of Contour Points (3006,0046) is required" in error_text
        assert "Contour Data (3006,0050) is required" in error_text
    
    def test_validate_contour_data_length_mismatch(self):
        """Test validation failure for contour data length not matching point count."""
        dataset = Dataset()
        roi_item = Dataset()
        roi_item.ReferencedROINumber = 1
        roi_item.ROIDisplayColor = [255, 0, 0]
        
        contour_item = Dataset()
        contour_item.ContourGeometricType = "CLOSED_PLANAR"
        contour_item.NumberOfContourPoints = 4
        contour_item.ContourData = [10.0, 10.0, 0.0, 20.0, 10.0, 0.0]  # Only 2 points worth of data
        
        roi_item.ContourSequence = [contour_item]
        dataset.ROIContourSequence = [roi_item]
        
        result = ROIContourValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) > 0
        assert any(
            "Contour Data length (6) should be 3 times Number of Contour Points (4)" in error
            for error in result.errors
        )
    
    def test_validate_point_geometric_type_constraints(self):
        """Test validation for POINT geometric type having exactly 1 point."""
        dataset = Dataset()
        roi_item = Dataset()
        roi_item.ReferencedROINumber = 1
        roi_item.ROIDisplayColor = [255, 0, 0]
        
        contour_item = Dataset()
        contour_item.ContourGeometricType = "POINT"
        contour_item.NumberOfContourPoints = 3  # Should be 1 for POINT type
        contour_item.ContourData = [10.0, 10.0, 0.0, 20.0, 10.0, 0.0, 30.0, 10.0, 0.0]
        
        roi_item.ContourSequence = [contour_item]
        dataset.ROIContourSequence = [roi_item]
        
        result = ROIContourValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) > 0
        assert any(
            "POINT geometric type must have exactly 1 contour point" in error
            for error in result.errors
        )
    
    def test_validate_open_contour_point_count_warning(self):
        """Test validation warning for open contours with insufficient points."""
        dataset = Dataset()
        roi_item = Dataset()
        roi_item.ReferencedROINumber = 1
        roi_item.ROIDisplayColor = [255, 0, 0]
        
        contour_item = Dataset()
        contour_item.ContourGeometricType = "OPEN_PLANAR"
        contour_item.NumberOfContourPoints = 1  # Should be at least 2 for open contours
        contour_item.ContourData = [10.0, 10.0, 0.0]
        
        roi_item.ContourSequence = [contour_item]
        dataset.ROIContourSequence = [roi_item]
        
        result = ROIContourValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.warnings) > 0
        assert any(
            "Open contours should have at least 2 points" in warning
            for warning in result.warnings
        )
    
    def test_validate_closed_contour_point_count_warning(self):
        """Test validation warning for closed contours with insufficient points."""
        dataset = Dataset()
        roi_item = Dataset()
        roi_item.ReferencedROINumber = 1
        roi_item.ROIDisplayColor = [255, 0, 0]
        
        contour_item = Dataset()
        contour_item.ContourGeometricType = "CLOSED_PLANAR"
        contour_item.NumberOfContourPoints = 2  # Should be at least 3 for closed contours
        contour_item.ContourData = [10.0, 10.0, 0.0, 20.0, 10.0, 0.0]
        
        roi_item.ContourSequence = [contour_item]
        dataset.ROIContourSequence = [roi_item]
        
        result = ROIContourValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.warnings) > 0
        assert any(
            "Closed contours should have at least 3 points" in warning
            for warning in result.warnings
        )
    
    def test_validate_xor_contour_consistency_error(self):
        """Test validation failure for mixed XOR and non-XOR contours in same ROI."""
        dataset = Dataset()
        roi_item = Dataset()
        roi_item.ReferencedROINumber = 1
        roi_item.ROIDisplayColor = [255, 0, 0]
        
        # Create mixed contours: one XOR and one non-XOR
        contour_item1 = Dataset()
        contour_item1.ContourGeometricType = "CLOSEDPLANAR_XOR"
        contour_item1.NumberOfContourPoints = 3
        contour_item1.ContourData = [10.0, 10.0, 0.0, 20.0, 10.0, 0.0, 15.0, 20.0, 0.0]
        
        contour_item2 = Dataset()
        contour_item2.ContourGeometricType = "CLOSED_PLANAR"  # Non-XOR
        contour_item2.NumberOfContourPoints = 3
        contour_item2.ContourData = [30.0, 30.0, 0.0, 40.0, 30.0, 0.0, 35.0, 40.0, 0.0]
        
        roi_item.ContourSequence = [contour_item1, contour_item2]
        dataset.ROIContourSequence = [roi_item]
        
        result = ROIContourValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) > 0
        assert any(
            "CLOSEDPLANAR_XOR, all contours in that ROI must be CLOSEDPLANAR_XOR" in error
            for error in result.errors
        )
    
    def test_validate_contour_offset_vector_length(self):
        """Test validation failure for invalid contour offset vector length."""
        dataset = Dataset()
        roi_item = Dataset()
        roi_item.ReferencedROINumber = 1
        roi_item.ROIDisplayColor = [255, 0, 0]
        
        contour_item = Dataset()
        contour_item.ContourGeometricType = "CLOSED_PLANAR"
        contour_item.NumberOfContourPoints = 3
        contour_item.ContourData = [10.0, 10.0, 0.0, 20.0, 10.0, 0.0, 15.0, 20.0, 0.0]
        contour_item.ContourOffsetVector = [1.0, 2.0]  # Should have 3 values (x,y,z)
        
        roi_item.ContourSequence = [contour_item]
        dataset.ROIContourSequence = [roi_item]
        
        result = ROIContourValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) > 0
        assert any(
            "Contour Offset Vector (3006,0045) must contain exactly 3 values" in error
            for error in result.errors
        )
    
    def test_validate_source_pixel_planes_usage_warning(self):
        """Test validation warning for Source Pixel Planes with non-useful geometric types."""
        dataset = Dataset()
        roi_item = Dataset()
        roi_item.ReferencedROINumber = 1
        roi_item.ROIDisplayColor = [255, 0, 0]
        
        # Add Source Pixel Planes Characteristics
        pixel_item = ROIContourModule.create_source_pixel_planes_characteristics_item(
            pixel_spacing=[1.0, 1.0],
            spacing_between_slices=2.5,
            image_orientation_patient=[1.0, 0.0, 0.0, 0.0, 1.0, 0.0],
            image_position_patient=[0.0, 0.0, 0.0],
            number_of_frames=100,
            rows=512,
            columns=512
        )
        roi_item.SourcePixelPlanesCharacteristicsSequence = [pixel_item]
        
        # Add contour with geometric type that makes pixel planes not useful
        contour_item = Dataset()
        contour_item.ContourGeometricType = "POINT"  # Not useful for pixel planes
        contour_item.NumberOfContourPoints = 1
        contour_item.ContourData = [10.0, 10.0, 0.0]
        
        roi_item.ContourSequence = [contour_item]
        dataset.ROIContourSequence = [roi_item]
        
        result = ROIContourValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.warnings) > 0
        assert any(
            "Source Pixel Planes Characteristics Sequence (3006,004A) is present but may not be useful" in warning
            for warning in result.warnings
        )
    
    def test_validate_source_series_sequence_requirements(self):
        """Test validation of Source Series Sequence requirements."""
        dataset = Dataset()
        roi_item = Dataset()
        roi_item.ReferencedROINumber = 1
        roi_item.ROIDisplayColor = [255, 0, 0]
        
        # Add Source Series Sequence without required Series Instance UID
        series_item = Dataset()
        # Missing SeriesInstanceUID
        roi_item.SourceSeriesSequence = [series_item]
        dataset.ROIContourSequence = [roi_item]
        
        result = ROIContourValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) > 0
        assert any(
            "Series Instance UID (0020,000E) is required" in error
            for error in result.errors
        )
    
    def test_validate_contour_image_sequence_requirements(self):
        """Test validation of Contour Image Sequence requirements."""
        dataset = Dataset()
        roi_item = Dataset()
        roi_item.ReferencedROINumber = 1
        roi_item.ROIDisplayColor = [255, 0, 0]
        
        contour_item = Dataset()
        contour_item.ContourGeometricType = "CLOSED_PLANAR"
        contour_item.NumberOfContourPoints = 3
        contour_item.ContourData = [10.0, 10.0, 0.0, 20.0, 10.0, 0.0, 15.0, 20.0, 0.0]
        
        # Add Contour Image Sequence without required SOP references
        image_item = Dataset()
        # Missing ReferencedSOPClassUID and ReferencedSOPInstanceUID
        contour_item.ContourImageSequence = [image_item]
        
        roi_item.ContourSequence = [contour_item]
        dataset.ROIContourSequence = [roi_item]
        
        result = ROIContourValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) >= 2
        
        error_text = ' '.join(result.errors)
        assert "Referenced SOP Class UID (0008,1150) is required" in error_text
        assert "Referenced SOP Instance UID (0008,1155) is required" in error_text
    
    def test_validation_config_enumerated_values_flag(self):
        """Test that ValidationConfig check_enumerated_values flag controls enum validation."""
        dataset = self.create_valid_dataset()
        
        # Modify to have invalid geometric type
        contour_item = dataset.ROIContourSequence[0].ContourSequence[0]
        contour_item.ContourGeometricType = "INVALID_TYPE"
        
        # Test with enumerated values checking disabled
        config = ValidationConfig(check_enumerated_values=False)
        result = ROIContourValidator.validate(dataset, config)
        
        # Should not have enumerated value warnings
        assert not any("Contour Geometric Type" in warning for warning in result.warnings)
    
    def test_validation_config_sequences_flag(self):
        """Test that ValidationConfig validate_sequences flag controls sequence validation."""
        dataset = Dataset()
        # Missing ROI Contour Sequence entirely
        
        # Test with sequence validation disabled
        config = ValidationConfig(validate_sequences=False)
        result = ROIContourValidator.validate(dataset, config)
        
        # Should not have sequence requirement errors
        assert not any("ROI Contour Sequence (3006,0039) is required" in error for error in result.errors)
    
    def test_validation_result_structure(self):
        """Test that ValidationResult has correct structure and types."""
        dataset = self.create_valid_dataset()
        
        result = ROIContourValidator.validate(dataset)
        
        # Test ValidationResult structure
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
        
        # Test error/warning methods exist
        assert hasattr(result, 'add_error')
        assert hasattr(result, 'add_warning')
        assert callable(result.add_error)
        assert callable(result.add_warning)
    
    def test_empty_dataset_validation(self):
        """Test validation of completely empty dataset."""
        dataset = Dataset()
        
        result = ROIContourValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) > 0
        # Should have error for missing required ROI Contour Sequence
        assert any("ROI Contour Sequence (3006,0039) is required" in error for error in result.errors)