"""RT Tolerance Tables Module DICOM validation - PS3.3 C.8.8.11"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.rt_enums import RTBeamLimitingDeviceType


class RTToleranceTablesValidator(BaseValidator):
    """Validator for DICOM RT Tolerance Tables Module (PS3.3 C.8.8.11)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate RT Tolerance Tables Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate enumerated values
        if config.check_enumerated_values:
            RTToleranceTablesValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            RTToleranceTablesValidator._validate_sequence_requirements(dataset, result)
        
        # Validate tolerance value consistency
        RTToleranceTablesValidator._validate_tolerance_value_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications."""
        
        tolerance_table_seq = getattr(dataset, 'ToleranceTableSequence', [])
        for i, table_item in enumerate(tolerance_table_seq):
            # Validate RT Beam Limiting Device Type in Beam Limiting Device Tolerance Sequence
            bld_tolerance_seq = table_item.get('BeamLimitingDeviceToleranceSequence', [])
            for j, bld_item in enumerate(bld_tolerance_seq):
                device_type = bld_item.get('RTBeamLimitingDeviceType', '')
                if device_type:
                    valid_device_types = [dtype.value for dtype in RTBeamLimitingDeviceType]
                    BaseValidator.validate_enumerated_value(
                        device_type, valid_device_types,
                        f"RT Beam Limiting Device Type (300A,00B8) in BLD Tolerance {j}, Table {i}", result
                    )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        tolerance_table_seq = getattr(dataset, 'ToleranceTableSequence', [])
        for i, table_item in enumerate(tolerance_table_seq):
            # Tolerance Table Number is Type 1
            if not table_item.get('ToleranceTableNumber'):
                result.add_error(
                    f"Tolerance Table Sequence item {i}: "
                    "Tolerance Table Number (300A,0042) is required"
                )
            
            # Validate Beam Limiting Device Tolerance Sequence
            bld_tolerance_seq = table_item.get('BeamLimitingDeviceToleranceSequence', [])
            for j, bld_item in enumerate(bld_tolerance_seq):
                # RT Beam Limiting Device Type is Type 1
                if not bld_item.get('RTBeamLimitingDeviceType'):
                    result.add_error(
                        f"Beam Limiting Device Tolerance Sequence item {j} in Table {i}: "
                        "RT Beam Limiting Device Type (300A,00B8) is required"
                    )
                
                # Beam Limiting Device Position Tolerance is Type 1
                if not bld_item.get('BeamLimitingDevicePositionTolerance'):
                    result.add_error(
                        f"Beam Limiting Device Tolerance Sequence item {j} in Table {i}: "
                        "Beam Limiting Device Position Tolerance (300A,004A) is required"
                    )
        
        # Validate uniqueness of tolerance table numbers
        table_numbers = []
        for i, table_item in enumerate(tolerance_table_seq):
            table_number = getattr(table_item, 'ToleranceTableNumber', None)
            if table_number is not None:
                if table_number in table_numbers:
                    result.add_error(
                        f"Tolerance Table Sequence item {i}: "
                        f"Tolerance Table Number ({table_number}) must be unique within the RT Plan"
                    )
                else:
                    table_numbers.append(table_number)
    
    @staticmethod
    def _validate_tolerance_value_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate tolerance value consistency and logical constraints."""
        
        tolerance_table_seq = getattr(dataset, 'ToleranceTableSequence', [])
        for i, table_item in enumerate(tolerance_table_seq):
            # Validate angular tolerance values are non-negative
            angular_tolerances = [
                ('GantryAngleTolerance', '300A,0044'),
                ('GantryPitchAngleTolerance', '300A,014E'),
                ('BeamLimitingDeviceAngleTolerance', '300A,0046'),
                ('PatientSupportAngleTolerance', '300A,004C'),
                ('TableTopEccentricAngleTolerance', '300A,004E'),
                ('TableTopPitchAngleTolerance', '300A,004F'),
                ('TableTopRollAngleTolerance', '300A,0050')
            ]
            
            for field_name, tag in angular_tolerances:
                tolerance_value = getattr(table_item, field_name, None)
                if tolerance_value is not None:
                    if tolerance_value < 0:
                        result.add_warning(
                            f"Tolerance Table Sequence item {i}: "
                            f"{field_name} ({tag}) should be non-negative"
                        )
                    elif tolerance_value > 360:
                        result.add_warning(
                            f"Tolerance Table Sequence item {i}: "
                            f"{field_name} ({tag}) exceeds 360 degrees"
                        )
            
            # Validate positional tolerance values are non-negative
            positional_tolerances = [
                ('TableTopVerticalPositionTolerance', '300A,0051'),
                ('TableTopLongitudinalPositionTolerance', '300A,0052'),
                ('TableTopLateralPositionTolerance', '300A,0053')
            ]
            
            for field_name, tag in positional_tolerances:
                tolerance_value = getattr(table_item, field_name, None)
                if tolerance_value is not None and tolerance_value < 0:
                    result.add_warning(
                        f"Tolerance Table Sequence item {i}: "
                        f"{field_name} ({tag}) should be non-negative"
                    )
            
            # Validate beam limiting device position tolerances
            bld_tolerance_seq = getattr(table_item, 'BeamLimitingDeviceToleranceSequence', [])
            for j, bld_item in enumerate(bld_tolerance_seq):
                position_tolerance = getattr(bld_item, 'BeamLimitingDevicePositionTolerance', None)
                if position_tolerance is not None and position_tolerance < 0:
                    result.add_warning(
                        f"Beam Limiting Device Tolerance Sequence item {j} in Table {i}: "
                        "Beam Limiting Device Position Tolerance (300A,004A) should be non-negative"
                    )
            
            # Check for reasonable tolerance values
            gantry_tolerance = getattr(table_item, 'GantryAngleTolerance', None)
            if gantry_tolerance is not None and gantry_tolerance > 10:
                result.add_warning(
                    f"Tolerance Table Sequence item {i}: "
                    f"Gantry Angle Tolerance ({gantry_tolerance} degrees) seems unusually large"
                )
            
            # Check for very small tolerance values that might be impractical
            for field_name, tag in angular_tolerances:
                tolerance_value = getattr(table_item, field_name, None)
                if tolerance_value is not None and tolerance_value < 0.01:
                    result.add_warning(
                        f"Tolerance Table Sequence item {i}: "
                        f"{field_name} ({tag}) value ({tolerance_value} degrees) seems unusually small"
                    )
            
            for field_name, tag in positional_tolerances:
                tolerance_value = getattr(table_item, field_name, None)
                if tolerance_value is not None and tolerance_value < 0.01:
                    result.add_warning(
                        f"Tolerance Table Sequence item {i}: "
                        f"{field_name} ({tag}) value ({tolerance_value} mm) seems unusually small"
                    )

        # Validate uniqueness of tolerance table numbers
        table_numbers = []
        for i, table_item in enumerate(tolerance_table_seq):
            table_number = getattr(table_item, 'ToleranceTableNumber', None)
            if table_number is not None:
                if table_number in table_numbers:
                    result.add_error(
                        f"Tolerance Table Sequence item {i}: "
                        f"Tolerance Table Number ({table_number}) must be unique within the RT Plan"
                    )
                else:
                    table_numbers.append(table_number)
