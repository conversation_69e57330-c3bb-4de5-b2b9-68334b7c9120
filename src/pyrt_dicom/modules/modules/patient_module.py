"""Patient Module - DICOM PS3.3 C.7.1.1

The Patient Module contains attributes of the Patient that are needed for
interpretation of the Composite Instances and are common for all Studies
performed on the Patient. This module contains Attributes of the Patient
that are needed for interpretation of the Composite Instances and are
common for all Studies performed on the Patient.
"""
from datetime import datetime, date
from pydicom import Dataset
from pydicom.valuerep import PersonName
from .base_module import BaseModule
from ...enums.patient_enums import PatientSex, ResponsiblePersonRole, TypeOfPatientID
from ...validators.modules.patient_validator import PatientValidator
from ...validators.modules.base_validator import ValidationConfig
from ...validators import ValidationResult
from ...utils.dicom_formatters import format_date_value, format_time_value, format_enum_value


class PatientModule(BaseModule):
    """Patient Module implementation for DICOM PS3.3 C.7.1.1.
    
    Contains attributes of the Patient that are needed for interpretation of
    Composite Instances and are common for all Studies performed on the Patient.
    This module contains Attributes that identify and describe the Patient who
    is the subject of the Study.
    
    Usage:
        # Create with required elements (all Type 2 - required but can be empty)
        patient = PatientModule.from_required_elements(
            patient_name="Doe^John",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE
        )
        
        # Add optional elements
        patient.with_optional_elements(
            type_of_patient_id=TypeOfPatientID.TEXT,
            quality_control_subject="NO"
        )
        
        # Add conditional elements for non-human organisms
        patient.with_non_human_organism(
            patient_species_description="Canis lupus familiaris",
            responsible_person="Dr. Smith"
        )
        
        # Generate dataset
        dataset = patient.to_dataset()
        
        # Validate
        result = patient.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        patient_name: str | PersonName = "",
        patient_id: str = "",
        patient_birth_date: str | datetime | date = "",
        patient_sex: str | PatientSex = ""
    ) -> 'PatientModule':
        """Create PatientModule from all required (Type 1 and Type 2) data elements.
        
        All elements in the Patient Module are Type 2 (required but can be empty)
        or conditional types. The core required elements are all Type 2.
        
        Args:
            patient_name (str | PersonName): Patient's full name (0010,0010) Type 2.
                Can be a string formatted as "Family^Given^Middle^Prefix^Suffix" 
                or a pydicom.valuerep.PersonName object.
            patient_id (str): Primary identifier for the Patient (0010,0020) Type 2
            patient_birth_date (str | datetime | date): Birth date of the Patient (0010,0030) Type 2.
                Can be a string in YYYYMMDD format, datetime object, or date object.
            patient_sex (str | PatientSex): Sex of the named Patient - M/F/O or PatientSex enum (0010,0040) Type 2
            
        Returns:
            PatientModule: New module instance with required data elements set
        """
        instance = cls()
        instance._dataset.PatientName = patient_name
        instance._dataset.PatientID = patient_id
        instance._dataset.PatientBirthDate = format_date_value(patient_birth_date)
        instance._dataset.PatientSex = format_enum_value(patient_sex)
        return instance
    
    def with_optional_elements(
        self,
        type_of_patient_id: str | TypeOfPatientID | None = None,
        referenced_patient_photo_sequence: list[Dataset] | None = None,
        quality_control_subject: str | None = None,
        referenced_patient_sequence: list[Dataset] | None = None,
        patient_birth_time: str | datetime | None = None,
        other_patient_ids_sequence: list[Dataset] | None = None,
        other_patient_names: str | PersonName | None = None,
        ethnic_group_code_sequence: list[Dataset] | None = None,
        ethnic_groups: str | None = None,
        patient_comments: str | None = None,
        strain_description: str | None = None,
        strain_nomenclature: str | None = None,
        strain_code_sequence: list[Dataset] | None = None,
        strain_additional_information: str | None = None,
        strain_stock_sequence: list[Dataset] | None = None,
        genetic_modifications_sequence: list[Dataset] | None = None,
        patient_identity_removed: str | None = None
    ) -> 'PatientModule':
        """Add optional (Type 3) data elements without conditional requirements.
        
        Args:
            type_of_patient_id (str | TypeOfPatientID | None): Type of identifier - TEXT/RFID/BARCODE or TypeOfPatientID enum (0010,0022)
            referenced_patient_photo_sequence (list[Dataset] | None): Photo to confirm identity (0010,1100)
            quality_control_subject (str | None): Quality control phantom - YES/NO (0010,0200)
            referenced_patient_sequence (list[Dataset] | None): Reference to a Patient (0008,1120)
            patient_birth_time (str | datetime | None): Birth time of Patient (0010,0032).
                Can be a string in HHMMSS format or datetime object.
                If datetime is provided, only the time portion is used.
            other_patient_ids_sequence (list[Dataset] | None): Additional patient identifiers (0010,1002)
            other_patient_names (str | PersonName | None): Other names for patient (0010,1001).
                Can be a string formatted as "Family^Given^Middle^Prefix^Suffix" 
                or a pydicom.valuerep.PersonName object.
            ethnic_group_code_sequence (list[Dataset] | None): Ethnic group codes (0010,2161)
            ethnic_groups (str | None): Ethnic group values (0010,2162)
            patient_comments (str | None): Additional patient information (0010,4000)
            strain_description (str | None): Strain of the Patient (0010,0212)
            strain_nomenclature (str | None): Nomenclature for strain (0010,0213)
            strain_code_sequence (list[Dataset] | None): Coded strain identification (0010,0219)
            strain_additional_information (str | None): Additional strain info (0010,0218)
            strain_stock_sequence (list[Dataset] | None): Strain stock information (0010,0216)
            genetic_modifications_sequence (list[Dataset] | None): Genetic modifications (0010,0221)
            patient_identity_removed (str | None): Identity removal flag - YES/NO (0012,0062)
            
        Returns:
            PatientModule: Self with optional elements added
        """
        if type_of_patient_id is not None:
            self._dataset.TypeOfPatientID = format_enum_value(type_of_patient_id)
        if referenced_patient_photo_sequence is not None:
            self._dataset.ReferencedPatientPhotoSequence = referenced_patient_photo_sequence
        if quality_control_subject is not None:
            self._dataset.QualityControlSubject = quality_control_subject
        if referenced_patient_sequence is not None:
            self._dataset.ReferencedPatientSequence = referenced_patient_sequence
        if patient_birth_time is not None:
            self._dataset.PatientBirthTime = format_time_value(patient_birth_time)
        if other_patient_ids_sequence is not None:
            self._dataset.OtherPatientIDsSequence = other_patient_ids_sequence
        if other_patient_names is not None:
            self._dataset.OtherPatientNames = other_patient_names
        if ethnic_group_code_sequence is not None:
            self._dataset.EthnicGroupCodeSequence = ethnic_group_code_sequence
        if ethnic_groups is not None:
            self._dataset.EthnicGroups = ethnic_groups
        if patient_comments is not None:
            self._dataset.PatientComments = patient_comments
        if strain_description is not None:
            self._dataset.StrainDescription = strain_description
        if strain_nomenclature is not None:
            self._dataset.StrainNomenclature = strain_nomenclature
        if strain_code_sequence is not None:
            self._dataset.StrainCodeSequence = strain_code_sequence
        if strain_additional_information is not None:
            self._dataset.StrainAdditionalInformation = strain_additional_information
        if strain_stock_sequence is not None:
            self._dataset.StrainStockSequence = strain_stock_sequence
        if genetic_modifications_sequence is not None:
            self._dataset.GeneticModificationsSequence = genetic_modifications_sequence
        if patient_identity_removed is not None:
            self._dataset.PatientIdentityRemoved = patient_identity_removed
        return self
    
    def with_alternative_calendar(
        self,
        patient_alternative_calendar: str,
        patient_birth_date_in_alternative_calendar: str | datetime | date | None = None,
        patient_death_date_in_alternative_calendar: str | datetime | date | None = None
    ) -> 'PatientModule':
        """Add alternative calendar dates with required calendar specification.
        
        Type 1C Requirement: Patient's Alternative Calendar (0010,0035) is required if either
        Patient's Birth Date in Alternative Calendar (0010,0033) or Patient's Death Date 
        in Alternative Calendar (0010,0034) is present.
        
        Args:
            patient_alternative_calendar (str): Alternative calendar used (0010,0035) Type 1C
            patient_birth_date_in_alternative_calendar (str | datetime | date | None): Birth date in alt calendar (0010,0033) Type 3.
                Can be a string, datetime object, or date object.
            patient_death_date_in_alternative_calendar (str | datetime | date | None): Death date in alt calendar (0010,0034) Type 3.
                Can be a string, datetime object, or date object.
            
        Returns:
            PatientModule: Self with alternative calendar elements added
            
        Raises:
            ValueError: If calendar is not specified but dates are provided
        """
        # Validate Type 1C requirement
        if (patient_birth_date_in_alternative_calendar is not None or 
            patient_death_date_in_alternative_calendar is not None):
            if not patient_alternative_calendar:
                raise ValueError(
                    "Patient's Alternative Calendar (0010,0035) is required when either "
                    "birth date or death date in alternative calendar is present (Type 1C requirement)"
                )
        
        self._dataset.PatientAlternativeCalendar = patient_alternative_calendar
        if patient_birth_date_in_alternative_calendar is not None:
            self._dataset.PatientBirthDateInAlternativeCalendar = format_date_value(patient_birth_date_in_alternative_calendar)
        if patient_death_date_in_alternative_calendar is not None:
            self._dataset.PatientDeathDateInAlternativeCalendar = format_date_value(patient_death_date_in_alternative_calendar)
        return self
    
    def with_non_human_organism(
        self,
        patient_species_description: str | None = None,
        patient_species_code_sequence: list[Dataset] | None = None,
        patient_breed_description: str | None = None,
        patient_breed_code_sequence: list[Dataset] | None = None,
        breed_registration_sequence: list[Dataset] | None = None,
        responsible_person: str | PersonName | None = None,
        responsible_organization: str | None = None
    ) -> 'PatientModule':
        """Add non-human organism data with all required conditional elements.
        
        Type 1C/2C Requirements for non-human organisms per DICOM PS3.3 C.7.1.1:
        - Type 1C: Patient Species Description (0010,2201) OR Patient Species Code Sequence (0010,2202)
        - Type 2C: Patient Breed Code Sequence (0010,2293) - required but may be empty
        - Type 2C: Patient Breed Description (0010,2292) - required if Breed Code Sequence is empty
        - Type 2C: Breed Registration Sequence (0010,2294) - required but may be empty
        - Type 2C: Responsible Person (0010,2297) - required but may be empty
        - Type 2C: Responsible Organization (0010,2299) - required but may be empty
        
        Args:
            patient_species_description (str | None): Taxonomic rank value (0010,2201) Type 1C
            patient_species_code_sequence (list[Dataset] | None): Coded taxonomic rank (0010,2202) Type 1C
            patient_breed_description (str | None): Breed description (0010,2292) Type 2C
            patient_breed_code_sequence (list[Dataset] | None): Coded breed (0010,2293) Type 2C
            breed_registration_sequence (list[Dataset] | None): Breed registry info (0010,2294) Type 2C
            responsible_person (str | PersonName | None): Person with authority (0010,2297) Type 2C
            responsible_organization (str | None): Organization with authority (0010,2299) Type 2C
            
        Returns:
            PatientModule: Self with non-human organism elements added
            
        Raises:
            ValueError: If required conditional elements are missing
        """
        # Type 1C requirement: Either species description OR species code sequence must be provided
        if patient_species_description is None and patient_species_code_sequence is None:
            raise ValueError(
                "Either patient_species_description or patient_species_code_sequence is required "
                "for non-human organisms (Type 1C requirement)"
            )
        
        # Set species information (Type 1C)
        if patient_species_description is not None:
            self._dataset.PatientSpeciesDescription = patient_species_description
        if patient_species_code_sequence is not None:
            self._dataset.PatientSpeciesCodeSequence = patient_species_code_sequence

        # Set breed information (Type 2C - required for non-human organisms)
        self._dataset.PatientBreedCodeSequence = patient_breed_code_sequence or []
        
        # Type 2C: Breed description required if breed code sequence is empty
        breed_code_seq = patient_breed_code_sequence or []
        if not breed_code_seq and patient_breed_description is None:
            raise ValueError(
                "patient_breed_description is required when patient_breed_code_sequence is empty "
                "for non-human organisms (Type 2C requirement)"
            )
        self._dataset.PatientBreedDescription = patient_breed_description or ""

        # Set registration and responsibility (Type 2C - required for non-human organisms)
        self._dataset.BreedRegistrationSequence = breed_registration_sequence or []
        self._dataset.ResponsiblePerson = responsible_person or ""
        self._dataset.ResponsibleOrganization = responsible_organization or ""
        
        return self
    
    def with_responsible_person(
        self,
        responsible_person: str | PersonName,
        responsible_person_role: str | ResponsiblePersonRole
    ) -> 'PatientModule':
        """Add responsible person with required role specification.
        
        Type 1C Requirement: Responsible Person Role (0010,2298) is required if 
        Responsible Person (0010,2297) is present and has a value.
        
        Args:
            responsible_person (str | PersonName): Name of person with authority (0010,2297).
                Can be a string formatted as "Family^Given^Middle^Prefix^Suffix" 
                or a pydicom.valuerep.PersonName object.
            responsible_person_role (str | ResponsiblePersonRole): Role or ResponsiblePersonRole enum (0010,2298) Type 1C
            
        Returns:
            PatientModule: Self with responsible person elements added
            
        Raises:
            ValueError: If responsible person has value but role is not provided
        """
        # Validate Type 1C requirement
        if responsible_person and not responsible_person_role:
            raise ValueError(
                "Responsible Person Role (0010,2298) is required when Responsible Person "
                "is present and has a value (Type 1C requirement)"
            )
        
        self._dataset.ResponsiblePerson = responsible_person
        if responsible_person_role:
            self._dataset.ResponsiblePersonRole = format_enum_value(responsible_person_role)
        return self
    
    def with_deidentification(
        self,
        patient_identity_removed: str | None = "YES",
        de_identification_method: str | None = None,
        de_identification_method_code_sequence: list[Dataset] | None = None
    ) -> 'PatientModule':
        """Add patient identity removal with required method specification.
        
        Type 1C Requirements per DICOM PS3.3 C.7.1.1:
        - De-identification Method (0012,0063): Required if Patient Identity Removed is "YES" 
          AND De-identification Method Code Sequence is not present
        - De-identification Method Code Sequence (0012,0064): Required if Patient Identity Removed 
          is "YES" AND De-identification Method is not present
        
        Args:
            patient_identity_removed (str | None): Identity removal flag - YES/NO (0012,0062) Type 3
            de_identification_method (str | None): Description of removal method (0012,0063) Type 1C
            de_identification_method_code_sequence (list[Dataset] | None): Coded removal method (0012,0064) Type 1C
            
        Returns:
            PatientModule: Self with deidentification elements added
            
        Raises:
            ValueError: If required conditional elements are missing
        """
        if patient_identity_removed is not None:
            self._dataset.PatientIdentityRemoved = patient_identity_removed
        
        # Type 1C requirement when identity removed is "YES"
        if patient_identity_removed == "YES":
            if de_identification_method is None and de_identification_method_code_sequence is None:
                raise ValueError(
                    "Either de_identification_method or de_identification_method_code_sequence "
                    "is required when patient_identity_removed is 'YES' (Type 1C requirement)"
                )

            if de_identification_method is not None:
                self._dataset.DeIdentificationMethod = de_identification_method
            if de_identification_method_code_sequence is not None:
                self._dataset.DeIdentificationMethodCodeSequence = de_identification_method_code_sequence
        
        return self
    
    @staticmethod
    def create_other_patient_id_item(
        patient_id: str,
        type_of_patient_id: str | TypeOfPatientID
    ) -> Dataset:
        """Create an item for Other Patient IDs Sequence (0010,1002).
        
        Args:
            patient_id (str): An identifier for the Patient (0010,0020) Type 1
            type_of_patient_id (str | TypeOfPatientID): Type of identifier - TEXT/RFID/BARCODE or TypeOfPatientID enum (0010,0022) Type 1
            
        Returns:
            Dataset: Sequence item with Patient ID and Type
        """
        item = Dataset()
        item.PatientID = patient_id
        if isinstance(type_of_patient_id, TypeOfPatientID):
            item.TypeOfPatientID = type_of_patient_id.value
        else:
            item.TypeOfPatientID = type_of_patient_id
        return item
    
    @staticmethod
    def create_strain_stock_item(
        strain_stock_number: str,
        strain_source: str,
        strain_source_registry_code: Dataset
    ) -> Dataset:
        """Create an item for Strain Stock Sequence (0010,0216).
        
        Args:
            strain_stock_number (str): Stock number of the strain (0010,0214) Type 1
            strain_source (str): Organization source of the organism (0010,0217) Type 1
            strain_source_registry_code (Dataset): Registry code sequence item (0010,0215) Type 1
            
        Returns:
            Dataset: Sequence item with strain stock information
        """
        item = Dataset()
        item.StrainStockNumber = strain_stock_number
        item.StrainSource = strain_source
        item.StrainSourceRegistryCodeSequence = [strain_source_registry_code]
        return item
    
    @staticmethod
    def create_genetic_modification_item(
        genetic_modifications_description: str,
        genetic_modifications_nomenclature: str,
        genetic_modifications_code_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create an item for Genetic Modifications Sequence (0010,0221).
        
        Args:
            genetic_modifications_description (str): Genetic modifications description (0010,0222) Type 1
            genetic_modifications_nomenclature (str): Nomenclature used (0010,0223) Type 1
            genetic_modifications_code_sequence (list[Dataset] | None): Coded identification (0010,0229) Type 3
            
        Returns:
            Dataset: Sequence item with genetic modification information
        """
        item = Dataset()
        item.GeneticModificationsDescription = genetic_modifications_description
        item.GeneticModificationsNomenclature = genetic_modifications_nomenclature
        
        if genetic_modifications_code_sequence is not None:
            item.GeneticModificationsCodeSequence = genetic_modifications_code_sequence
        
        return item
    
    @property
    def is_human(self) -> bool:
        """Check if this patient represents a human organism.
        
        Returns:
            bool: True if no species information indicates human organism
        """
        return not self.is_non_human
    
    @property
    def is_non_human(self) -> bool:
        """Check if this patient represents a non-human organism.
        
        Returns:
            bool: True if species information indicates non-human organism
        """
        return (hasattr(self._dataset, 'PatientSpeciesDescription') or 
                hasattr(self._dataset, 'PatientSpeciesCodeSequence'))
    
    @property
    def is_deidentified(self) -> bool:
        """Check if patient identity has been removed.
        
        Returns:
            bool: True if PatientIdentityRemoved is "YES"
        """
        return getattr(self._dataset, 'PatientIdentityRemoved', '') == "YES"
    
    @property
    def has_alternative_calendar_dates(self) -> bool:
        """Check if alternative calendar dates are present.
        
        Returns:
            bool: True if birth or death date in alternative calendar is present
        """
        return (hasattr(self._dataset, 'PatientBirthDateInAlternativeCalendar') or
                hasattr(self._dataset, 'PatientDeathDateInAlternativeCalendar'))
    
    @property
    def has_responsible_person_with_role(self) -> bool:
        """Check if responsible person is present with required role.
        
        Returns:
            bool: True if both responsible person and role are present
        """
        has_person = hasattr(self._dataset, 'ResponsiblePerson') and self._dataset.ResponsiblePerson
        has_role = hasattr(self._dataset, 'ResponsiblePersonRole') and self._dataset.ResponsiblePersonRole
        return has_person and has_role
    
    @property
    def requires_alternative_calendar(self) -> bool:
        """Check if alternative calendar specification is required (Type 1C).
        
        Returns:
            bool: True if alternative calendar dates are present, requiring calendar specification
        """
        return self.has_alternative_calendar_dates
    
    @property
    def requires_deidentification_method(self) -> bool:
        """Check if deidentification method is required (Type 1C).
        
        Returns:
            bool: True if patient identity is removed, requiring method specification
        """
        return self.is_deidentified
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this Patient Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return PatientValidator.validate(self._dataset, config)