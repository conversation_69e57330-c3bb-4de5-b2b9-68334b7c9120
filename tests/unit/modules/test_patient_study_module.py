"""
Test PatientStudyModule functionality.

PatientStudyModule implements DICOM PS3.3 C.7.2.2 Patient Study Module.
Contains attributes that provide information about the Patient at the time the Study started.
"""

import pydicom
from pyrt_dicom.modules import PatientStudyModule
from pyrt_dicom.enums.patient_study_enums import SmokingStatus, PregnancyStatus, PatientSexNeutered
from pyrt_dicom.validators import ValidationResult


class TestPatientStudyModule:
    """Test PatientStudyModule functionality."""

    def test_from_required_elements_success(self):
        """Test successful creation with required elements (none - all are Type 2C/3)."""
        patient_study = PatientStudyModule.from_required_elements()

        # Module should be created successfully with no required elements
        assert patient_study is not None
        assert isinstance(patient_study, PatientStudyModule)

        # Test dataset generation
        dataset = patient_study.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)

    def test_with_optional_elements_validation(self):
        """Test that with_optional_elements redirects to specialized methods."""
        patient_study = PatientStudyModule.from_required_elements()

        # Should work with no arguments
        result = patient_study.with_optional_elements()
        assert result is patient_study

        # Should raise error with arguments - must use specialized methods
        try:
            patient_study.with_optional_elements(patients_age="045Y")
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "specialized methods" in str(e)
    
    def test_with_patient_demographics(self):
        """Test adding patient demographic information."""
        patient_study = PatientStudyModule.from_required_elements()

        # Test basic demographics
        patient_study.with_patient_demographics(
            patients_age="045Y",
            patients_size=1.75,
            patients_weight=70.5,
            smoking_status=SmokingStatus.NO
        )

        # Test via dataset generation
        dataset = patient_study.to_dataset()
        assert dataset.PatientAge == "045Y"
        assert dataset.PatientSize == 1.75
        assert dataset.PatientWeight == 70.5
        assert dataset.SmokingStatus == "NO"
        assert patient_study.has_demographic_info

    def test_with_medical_information(self):
        """Test adding medical information."""
        patient_study = PatientStudyModule.from_required_elements()

        patient_study.with_medical_information(
            medical_alerts="Drug allergies: Penicillin",
            allergies="Penicillin, Shellfish",
            additional_patient_history="Previous surgery in 2020"
        )

        # Test via dataset generation
        dataset = patient_study.to_dataset()
        assert dataset.MedicalAlerts == "Drug allergies: Penicillin"
        assert dataset.Allergies == "Penicillin, Shellfish"
        assert dataset.AdditionalPatientHistory == "Previous surgery in 2020"
        assert patient_study.has_medical_info

    def test_with_pregnancy_information(self):
        """Test adding pregnancy information."""
        patient_study = PatientStudyModule.from_required_elements()

        patient_study.with_pregnancy_information(
            pregnancy_status=PregnancyStatus.NOT_PREGNANT,
            last_menstrual_date="20240101"
        )

        # Test via dataset generation
        dataset = patient_study.to_dataset()
        assert dataset.PregnancyStatus == 1  # NOT_PREGNANT value
        assert dataset.LastMenstrualDate == "20240101"
        assert patient_study.has_pregnancy_info

    def test_with_visit_information(self):
        """Test adding visit information."""
        patient_study = PatientStudyModule.from_required_elements()

        patient_study.with_visit_information(
            admission_id="ADM001",
            reason_for_visit="Routine checkup",
            service_episode_id="EP001"
        )

        # Test via dataset generation
        dataset = patient_study.to_dataset()
        assert dataset.AdmissionID == "ADM001"
        assert dataset.ReasonForVisit == "Routine checkup"
        assert dataset.ServiceEpisodeID == "EP001"
        assert patient_study.has_visit_info
    
    def test_with_non_human_organism_info(self):
        """Test adding non-human organism information."""
        patient_study = PatientStudyModule.from_required_elements()

        patient_study.with_non_human_organism_info(
            patients_sex_neutered=PatientSexNeutered.ALTERED
        )

        # Test via dataset generation
        dataset = patient_study.to_dataset()
        assert dataset.PatientSexNeutered == "ALTERED"
        assert patient_study.is_non_human_organism

    def test_smoking_status_enum_values(self):
        """Test all smoking status enum values."""
        patient_study = PatientStudyModule.from_required_elements()

        smoking_values = [SmokingStatus.YES, SmokingStatus.NO, SmokingStatus.UNKNOWN]

        for smoking_status in smoking_values:
            patient_study.with_patient_demographics(smoking_status=smoking_status)
            dataset = patient_study.to_dataset()
            assert dataset.SmokingStatus == smoking_status.value

    def test_pregnancy_status_enum_values(self):
        """Test all pregnancy status enum values."""
        patient_study = PatientStudyModule.from_required_elements()

        pregnancy_values = [
            PregnancyStatus.NOT_PREGNANT,
            PregnancyStatus.POSSIBLY_PREGNANT,
            PregnancyStatus.DEFINITELY_PREGNANT,
            PregnancyStatus.UNKNOWN
        ]

        for pregnancy_status in pregnancy_values:
            patient_study.with_pregnancy_information(pregnancy_status=pregnancy_status)
            dataset = patient_study.to_dataset()
            assert dataset.PregnancyStatus == pregnancy_status.value
    
    def test_patient_sex_neutered_enum_values(self):
        """Test all patient sex neutered enum values."""
        patient_study = PatientStudyModule.from_required_elements()

        sex_neutered_values = [PatientSexNeutered.ALTERED, PatientSexNeutered.UNALTERED]

        for sex_neutered in sex_neutered_values:
            patient_study.with_non_human_organism_info(patients_sex_neutered=sex_neutered)
            dataset = patient_study.to_dataset()
            assert dataset.PatientSexNeutered == sex_neutered.value

    def test_property_helpers(self):
        """Test property helper methods."""
        patient_study = PatientStudyModule.from_required_elements()

        # Initially no information
        assert not patient_study.has_demographic_info
        assert not patient_study.has_medical_info
        assert not patient_study.has_pregnancy_info
        assert not patient_study.has_visit_info
        assert not patient_study.is_non_human_organism
        assert not patient_study.has_gender_identity_info
        assert not patient_study.has_sex_parameters_for_clinical_use
        assert not patient_study.has_person_names_to_use
        assert not patient_study.has_third_person_pronouns

        # Add demographic info
        patient_study.with_patient_demographics(patients_age="030Y")
        assert patient_study.has_demographic_info

        # Add medical info
        patient_study.with_medical_information(allergies="None known")
        assert patient_study.has_medical_info

        # Add pregnancy info
        patient_study.with_pregnancy_information(pregnancy_status=PregnancyStatus.UNKNOWN)
        assert patient_study.has_pregnancy_info

        # Add visit info
        patient_study.with_visit_information(admission_id="TEST001")
        assert patient_study.has_visit_info

        # Add non-human organism info
        patient_study.with_non_human_organism_info(patients_sex_neutered=PatientSexNeutered.UNALTERED)
        assert patient_study.is_non_human_organism
    
    def test_method_chaining(self):
        """Test that methods can be chained together."""
        patient_study = PatientStudyModule.from_required_elements().with_patient_demographics(
            patients_age="035Y",
            patients_weight=65.0
        ).with_medical_information(
            allergies="None known"
        ).with_visit_information(
            reason_for_visit="Annual exam"
        )

        # Test via dataset generation
        dataset = patient_study.to_dataset()
        assert dataset.PatientAge == "035Y"
        assert dataset.PatientWeight == 65.0
        assert dataset.Allergies == "None known"
        assert dataset.ReasonForVisit == "Annual exam"

    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        patient_study = PatientStudyModule.from_required_elements()

        assert hasattr(patient_study, 'validate')
        assert callable(patient_study.validate)

        # Test validation result structure
        validation_result = patient_study.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)

    def test_optional_none_values_ignored(self):
        """Test that None values are ignored in optional methods."""
        patient_study = PatientStudyModule.from_required_elements()

        # None values should not set attributes
        patient_study.with_patient_demographics(
            patients_age="045Y",
            patients_size=None,  # Should be ignored
            patients_weight=70.0
        )

        # Test via dataset generation
        dataset = patient_study.to_dataset()
        assert dataset.PatientAge == "045Y"
        assert not hasattr(dataset, 'PatientSize')
        assert dataset.PatientWeight == 70.0
    
    def test_create_sequence_items(self):
        """Test static methods for creating sequence items."""
        # Test gender identity item creation
        gender_identity_item = PatientStudyModule.create_gender_identity_item(
            gender_identity_code_sequence=[{"CodeValue": "F", "CodeMeaning": "Female"}],
            gender_identity_comment="Test comment"
        )

        assert hasattr(gender_identity_item, 'GenderIdentityCodeSequence')
        assert hasattr(gender_identity_item, 'GenderIdentityComment')
        assert gender_identity_item.GenderIdentityComment == "Test comment"

        # Test person name item creation
        name_item = PatientStudyModule.create_person_name_to_use_item(
            name_to_use="Jane Doe",
            name_to_use_comment="Preferred name"
        )

        assert hasattr(name_item, 'NameToUse')
        assert hasattr(name_item, 'NameToUseComment')
        assert name_item.NameToUse == "Jane Doe"
        assert name_item.NameToUseComment == "Preferred name"

    def test_dataset_generation(self):
        """Test that to_dataset() generates proper pydicom Dataset."""
        patient_study = PatientStudyModule.from_required_elements()

        # Add some data
        patient_study.with_patient_demographics(
            patients_age="045Y",
            patients_weight=70.5
        ).with_medical_information(
            allergies="None known"
        )

        # Generate dataset
        dataset = patient_study.to_dataset()

        # Verify dataset type and content
        assert isinstance(dataset, pydicom.Dataset)
        assert len(dataset) == 3  # Should have 3 elements
        assert dataset.PatientAge == "045Y"
        assert dataset.PatientWeight == 70.5
        assert dataset.Allergies == "None known"

        # Verify dataset is a copy (not reference)
        dataset.PatientAge = "050Y"
        dataset2 = patient_study.to_dataset()
        assert dataset2.PatientAge == "045Y"  # Should still be original value
