"""
Test GeneralEquipmentModule (M - Mandatory) functionality.

GeneralEquipmentModule implements DICOM PS3.3 C.7.5.1 General Equipment Module.
Required for all RTDoseIOD instances.
"""

import pytest
import pydicom
from pyrt_dicom.modules import GeneralEquipmentModule
from pyrt_dicom.validators import ValidationResult


class TestGeneralEquipmentModule:
    """Test GeneralEquipmentModule (M - Mandatory) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        )

        dataset = equipment.to_dataset()
        assert dataset.Manufacturer == "Test Manufacturer"
    
    def test_with_optional_elements(self):
        """Test adding optional equipment information."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Varian Medical Systems"
        ).with_optional_elements(
            institution_name="Test Hospital",
            station_name="TrueBeam STx",
            manufacturers_model_name="TrueBeam",
            device_serial_number="12345",
            software_versions="v2.7.1"
        )

        dataset = equipment.to_dataset()
        assert dataset.InstitutionName == "Test Hospital"
        assert dataset.StationName == "TrueBeam STx"
        assert dataset.ManufacturerModelName == "TrueBeam"
        assert dataset.DeviceSerialNumber == "12345"
        assert dataset.SoftwareVersions == "v2.7.1"
    
    def test_various_manufacturers(self):
        """Test various manufacturer names."""
        manufacturers = [
            "Varian Medical Systems",
            "Elekta",
            "Siemens Healthineers",
            "Philips Healthcare",
            "GE Healthcare",
            "RaySearch Laboratories"
        ]

        for manufacturer in manufacturers:
            equipment = GeneralEquipmentModule.from_required_elements(
                manufacturer=manufacturer
            )
            dataset = equipment.to_dataset()
            assert dataset.Manufacturer == manufacturer
    
    def test_treatment_planning_systems(self):
        """Test equipment information for treatment planning systems."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Eclipse Treatment Planning System"
        ).with_optional_elements(
            station_name="TPS-01",
            manufacturers_model_name="Eclipse",
            software_versions="v16.1.0"
        )

        dataset = equipment.to_dataset()
        assert "Eclipse" in dataset.Manufacturer
        assert dataset.StationName == "TPS-01"
        assert dataset.SoftwareVersions == "v16.1.0"
    
    def test_linac_equipment_info(self):
        """Test equipment information for linear accelerators."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Varian Medical Systems"
        ).with_optional_elements(
            station_name="TrueBeam STx #1",
            manufacturers_model_name="TrueBeam STx",
            device_serial_number="TB001234",
            software_versions="v2.7.1",
            institution_name="Cancer Treatment Center"
        )

        dataset = equipment.to_dataset()
        assert dataset.Manufacturer == "Varian Medical Systems"
        assert "TrueBeam" in dataset.StationName
        assert "STx" in dataset.ManufacturerModelName
        assert dataset.DeviceSerialNumber.startswith("TB")
    
    def test_empty_manufacturer_handling(self):
        """Test handling of empty manufacturer (Type 2)."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer=""  # Empty but not None
        )

        dataset = equipment.to_dataset()
        assert dataset.Manufacturer == ""
    
    def test_institutional_department_info(self):
        """Test institutional and department information."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        ).with_optional_elements(
            institution_name="University Medical Center",
            institutional_department_name="Radiation Oncology",
            station_name="Physics WS"
        )

        dataset = equipment.to_dataset()
        assert dataset.InstitutionName == "University Medical Center"
        assert dataset.InstitutionalDepartmentName == "Radiation Oncology"
        assert dataset.StationName == "Physics WS"
    
    def test_software_version_formats(self):
        """Test various software version formats."""
        software_versions = [
            "v1.0",
            "v2.7.1",
            "16.1.0",
            "2024.1",
            "R2023b",
            "Build 12345"
        ]

        for version in software_versions:
            equipment = GeneralEquipmentModule.from_required_elements(
                manufacturer="Test Manufacturer"
            ).with_optional_elements(
                software_versions=version
            )
            dataset = equipment.to_dataset()
            assert dataset.SoftwareVersions == version
    
    def test_device_serial_number_formats(self):
        """Test various device serial number formats."""
        serial_numbers = [
            "12345",
            "TB001234",
            "SN-ABC-123",
            "VMS-001-2024",
            "LINAC001"
        ]

        for serial in serial_numbers:
            equipment = GeneralEquipmentModule.from_required_elements(
                manufacturer="Test Manufacturer"
            ).with_optional_elements(
                device_serial_number=serial
            )
            dataset = equipment.to_dataset()
            assert dataset.DeviceSerialNumber == serial
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        )
        
        assert hasattr(equipment, 'validate')
        assert callable(equipment.validate)
        
        # Test validation result structure
        validation_result = equipment.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_equipment_for_dose_calculation(self):
        """Test equipment information specific to dose calculation."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="RayStation Treatment Planning System"
        ).with_optional_elements(
            manufacturers_model_name="RayStation",
            software_versions="v12.0.0",
            station_name="DOSE-CALC-01"
        )

        # Verify equipment suitable for dose calculation
        dataset = equipment.to_dataset()
        assert "RayStation" in dataset.Manufacturer
        assert dataset.StationName == "DOSE-CALC-01"
        assert dataset.SoftwareVersions == "v12.0.0"

    def test_to_dataset_method(self):
        """Test that to_dataset() returns a proper pydicom Dataset."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        ).with_optional_elements(
            institution_name="Test Hospital",
            station_name="Test Station"
        )

        dataset = equipment.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)
        assert len(dataset) == 3  # Manufacturer, InstitutionName, StationName
        assert dataset.Manufacturer == "Test Manufacturer"
        assert dataset.InstitutionName == "Test Hospital"
        assert dataset.StationName == "Test Station"

    def test_dataset_independence(self):
        """Test that to_dataset() returns independent copies."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Original Manufacturer"
        )

        dataset1 = equipment.to_dataset()
        dataset2 = equipment.to_dataset()

        # Modify one dataset
        dataset1.Manufacturer = "Modified Manufacturer"

        # Other dataset should be unchanged
        assert dataset2.Manufacturer == "Original Manufacturer"

        # Original module should be unchanged
        dataset3 = equipment.to_dataset()
        assert dataset3.Manufacturer == "Original Manufacturer"

    def test_property_methods(self):
        """Test property methods for checking module state."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        )

        # Initially no optional info
        assert not equipment.has_institution_info
        assert not equipment.has_device_identification
        assert not equipment.has_calibration_info
        assert not equipment.has_pixel_padding

        # Add institution info
        equipment.with_optional_elements(institution_name="Test Hospital")
        assert equipment.has_institution_info

        # Add device identification
        equipment.with_optional_elements(device_serial_number="12345")
        assert equipment.has_device_identification

        # Add calibration info
        equipment.with_optional_elements(date_of_last_calibration=["20240101"])
        assert equipment.has_calibration_info
        assert equipment.has_calibration_pairing  # Date alone is fine

        # Add pixel padding
        equipment.with_pixel_padding(pixel_padding_value=0)
        assert equipment.has_pixel_padding

    def test_calibration_pairing_validation(self):
        """Test calibration date/time pairing validation."""
        equipment = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        )

        # No calibration info - should be fine
        assert equipment.has_calibration_pairing

        # Date only - should be fine
        equipment.with_optional_elements(date_of_last_calibration=["20240101"])
        assert equipment.has_calibration_pairing

        # Time only - should fail pairing
        equipment2 = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        ).with_optional_elements(time_of_last_calibration=["120000"])
        assert not equipment2.has_calibration_pairing

        # Paired date and time - should be fine
        equipment3 = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        ).with_optional_elements(
            date_of_last_calibration=["20240101", "20240201"],
            time_of_last_calibration=["120000", "130000"]
        )
        assert equipment3.has_calibration_pairing

        # Mismatched counts - should fail pairing
        equipment4 = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        ).with_optional_elements(
            date_of_last_calibration=["20240101"],
            time_of_last_calibration=["120000", "130000"]
        )
        assert not equipment4.has_calibration_pairing