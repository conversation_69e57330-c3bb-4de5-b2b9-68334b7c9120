"""RT <PERSON><PERSON><PERSON> Observations Module DICOM validation - PS3.3 C.8.8.8"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.rt_enums import RTROIInterpretedType, RTROIRelationship, ROIPhysicalProperty


class RTROIObservationsValidator(BaseValidator):
    """Validator for DICOM RT ROI Observations Module (PS3.3 C.8.8.8)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate RT ROI Observations Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            RTROIObservationsValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            RTROIObservationsValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            RTROIObservationsValidator._validate_sequence_requirements(dataset, result)
        
        # Validate physical properties consistency
        RTROIObservationsValidator._validate_physical_properties_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C conditional requirements."""
        
        # ROI Interpreter Sequence is Type 1C - required if ROI Creator Sequence is present
        # Note: ROI Creator Sequence is in Structure Set Module, so we can't validate this here
        # This would need to be validated at the IOD level
        
        rt_roi_obs_seq = getattr(dataset, 'RTROIObservationsSequence', [])
        for i, obs_item in enumerate(rt_roi_obs_seq):
            # Validate ROI Physical Properties Sequence conditional requirements
            roi_physical_props_seq = obs_item.get('ROIPhysicalPropertiesSequence', [])
            for j, props_item in enumerate(roi_physical_props_seq):
                roi_physical_property = props_item.get('ROIPhysicalProperty', '')
                roi_elemental_comp_seq = props_item.get('ROIElementalCompositionSequence', [])
                
                # Type 1C: ROI Elemental Composition Sequence required if property is ELEM_FRACTION
                if roi_physical_property == 'ELEM_FRACTION' and not roi_elemental_comp_seq:
                    result.add_error(
                        f"ROI Physical Properties Sequence item {j} in Observation {i}: "
                        "ROI Elemental Composition Sequence (3006,00B6) is required when "
                        "ROI Physical Property is ELEM_FRACTION"
                    )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications."""
        
        rt_roi_obs_seq = getattr(dataset, 'RTROIObservationsSequence', [])
        for i, obs_item in enumerate(rt_roi_obs_seq):
            # RT ROI Interpreted Type (3006,00A4)
            interpreted_type = obs_item.get('RTROIInterpretedType', '')
            if interpreted_type:
                valid_types = [itype.value for itype in RTROIInterpretedType]
                BaseValidator.validate_enumerated_value(
                    interpreted_type, valid_types,
                    f"RT ROI Interpreted Type (3006,00A4) in Observation {i}", result
                )
            
            # RT ROI Relationship in RT Related ROI Sequence
            rt_related_roi_seq = obs_item.get('RTRelatedROISequence', [])
            for j, related_item in enumerate(rt_related_roi_seq):
                roi_relationship = related_item.get('RTROIRelationship', '')
                if roi_relationship:
                    valid_relationships = [rel.value for rel in RTROIRelationship]
                    BaseValidator.validate_enumerated_value(
                        roi_relationship, valid_relationships,
                        f"RT ROI Relationship (3006,0033) in Related ROI {j}, Observation {i}", result
                    )
            
            # ROI Physical Property in ROI Physical Properties Sequence
            roi_physical_props_seq = obs_item.get('ROIPhysicalPropertiesSequence', [])
            for j, props_item in enumerate(roi_physical_props_seq):
                physical_property = props_item.get('ROIPhysicalProperty', '')
                if physical_property:
                    valid_properties = [prop.value for prop in ROIPhysicalProperty]
                    BaseValidator.validate_enumerated_value(
                        physical_property, valid_properties,
                        f"ROI Physical Property (3006,00B2) in Properties {j}, Observation {i}", result
                    )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        rt_roi_obs_seq = getattr(dataset, 'RTROIObservationsSequence', [])
        for i, obs_item in enumerate(rt_roi_obs_seq):
            # Observation Number is Type 1
            if not obs_item.get('ObservationNumber'):
                result.add_error(
                    f"RT ROI Observations Sequence item {i}: "
                    "Observation Number (3006,0082) is required"
                )
            
            # Referenced ROI Number is Type 1
            if not obs_item.get('ReferencedROINumber'):
                result.add_error(
                    f"RT ROI Observations Sequence item {i}: "
                    "Referenced ROI Number (3006,0084) is required"
                )
            
            # RT ROI Interpreted Type is Type 2 (required but may be empty)
            if 'RTROIInterpretedType' not in obs_item:
                result.add_error(
                    f"RT ROI Observations Sequence item {i}: "
                    "RT ROI Interpreted Type (3006,00A4) is required (Type 2)"
                )
            
            # ROI Interpreter is Type 2 (required but may be empty)
            if 'ROIInterpreter' not in obs_item:
                result.add_error(
                    f"RT ROI Observations Sequence item {i}: "
                    "ROI Interpreter (3006,00A6) is required (Type 2)"
                )
            
            # Validate RT Related ROI Sequence
            rt_related_roi_seq = obs_item.get('RTRelatedROISequence', [])
            for j, related_item in enumerate(rt_related_roi_seq):
                if not related_item.get('ReferencedROINumber'):
                    result.add_error(
                        f"RT Related ROI Sequence item {j} in Observation {i}: "
                        "Referenced ROI Number (3006,0084) is required"
                    )
            
            # Validate ROI Physical Properties Sequence
            roi_physical_props_seq = obs_item.get('ROIPhysicalPropertiesSequence', [])
            for j, props_item in enumerate(roi_physical_props_seq):
                if not props_item.get('ROIPhysicalProperty'):
                    result.add_error(
                        f"ROI Physical Properties Sequence item {j} in Observation {i}: "
                        "ROI Physical Property (3006,00B2) is required"
                    )
                if not props_item.get('ROIPhysicalPropertyValue'):
                    result.add_error(
                        f"ROI Physical Properties Sequence item {j} in Observation {i}: "
                        "ROI Physical Property Value (3006,00B4) is required"
                    )
                
                # Validate ROI Elemental Composition Sequence
                roi_elemental_comp_seq = props_item.get('ROIElementalCompositionSequence', [])
                for k, comp_item in enumerate(roi_elemental_comp_seq):
                    if not comp_item.get('ROIElementalCompositionAtomicNumber'):
                        result.add_error(
                            f"ROI Elemental Composition Sequence item {k} in Properties {j}, Observation {i}: "
                            "ROI Elemental Composition Atomic Number (3006,00B7) is required"
                        )
                    if not comp_item.get('ROIElementalCompositionAtomicMassFraction'):
                        result.add_error(
                            f"ROI Elemental Composition Sequence item {k} in Properties {j}, Observation {i}: "
                            "ROI Elemental Composition Atomic Mass Fraction (3006,00B8) is required"
                        )
            
            # Validate Related RT ROI Observations Sequence
            related_obs_seq = obs_item.get('RelatedRTROIObservationsSequence', [])
            for j, related_obs_item in enumerate(related_obs_seq):
                if not related_obs_item.get('ObservationNumber'):
                    result.add_error(
                        f"Related RT ROI Observations Sequence item {j} in Observation {i}: "
                        "Observation Number (3006,0082) is required"
                    )

            # Validate ROI Interpreter Sequence
            roi_interpreter_seq = obs_item.get('ROIInterpreterSequence', [])
            for j, interpreter_item in enumerate(roi_interpreter_seq):
                if not interpreter_item.get('ROIInterpreter'):
                    result.add_error(
                        f"ROI Interpreter Sequence item {j} in Observation {i}: "
                        "ROI Interpreter (3006,00A6) is required"
                    )

        # Validate uniqueness of observation numbers
        observation_numbers = []
        for i, obs_item in enumerate(rt_roi_obs_seq):
            obs_number = obs_item.get('ObservationNumber')
            if obs_number is not None:
                if obs_number in observation_numbers:
                    result.add_error(
                        f"RT ROI Observations Sequence item {i}: "
                        f"Observation Number ({obs_number}) must be unique within the RT Plan"
                    )
                else:
                    observation_numbers.append(obs_number)
    
    @staticmethod
    def _validate_physical_properties_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate physical properties consistency and logical constraints."""
        
        rt_roi_obs_seq = getattr(dataset, 'RTROIObservationsSequence', [])
        for i, obs_item in enumerate(rt_roi_obs_seq):
            roi_physical_props_seq = obs_item.get('ROIPhysicalPropertiesSequence', [])
            
            for j, props_item in enumerate(roi_physical_props_seq):
                # Validate elemental composition mass fractions sum to 1.0
                roi_elemental_comp_seq = props_item.get('ROIElementalCompositionSequence', [])
                if roi_elemental_comp_seq:
                    total_mass_fraction = 0.0
                    for comp_item in roi_elemental_comp_seq:
                        mass_fraction = comp_item.get('ROIElementalCompositionAtomicMassFraction', 0.0)
                        total_mass_fraction += mass_fraction
                    
                    # Check if sum is approximately 1.0 (within floating point precision)
                    if abs(total_mass_fraction - 1.0) > 0.001:
                        result.add_warning(
                            f"ROI Physical Properties Sequence item {j} in Observation {i}: "
                            f"Sum of ROI Elemental Composition Atomic Mass Fractions ({total_mass_fraction:.3f}) "
                            "should equal 1.0"
                        )
                
                # Validate atomic numbers are valid (1-118)
                for k, comp_item in enumerate(roi_elemental_comp_seq):
                    atomic_number = comp_item.get('ROIElementalCompositionAtomicNumber', 0)
                    if atomic_number < 1 or atomic_number > 118:
                        result.add_warning(
                            f"ROI Elemental Composition Sequence item {k} in Properties {j}, Observation {i}: "
                            f"Atomic Number ({atomic_number}) should be between 1 and 118"
                        )
                    
                    mass_fraction = comp_item.get('ROIElementalCompositionAtomicMassFraction', 0.0)
                    if mass_fraction < 0.0 or mass_fraction > 1.0:
                        result.add_warning(
                            f"ROI Elemental Composition Sequence item {k} in Properties {j}, Observation {i}: "
                            f"Atomic Mass Fraction ({mass_fraction}) should be between 0.0 and 1.0"
                        )
                
                # Validate physical property values are reasonable
                physical_property = props_item.get('ROIPhysicalProperty', '')
                property_value = props_item.get('ROIPhysicalPropertyValue', 0.0)
                
                if physical_property in ['REL_MASS_DENSITY', 'REL_ELEC_DENSITY'] and property_value < 0:
                    result.add_warning(
                        f"ROI Physical Properties Sequence item {j} in Observation {i}: "
                        f"Physical property value ({property_value}) should be non-negative for {physical_property}"
                    )
                elif physical_property == 'EFFECTIVE_Z' and (property_value < 1 or property_value > 118):
                    result.add_warning(
                        f"ROI Physical Properties Sequence item {j} in Observation {i}: "
                        f"Effective Z value ({property_value}) should be between 1 and 118"
                    )

            # Validate ROI Observation DateTime format
            roi_obs_datetime = obs_item.get('ROIObservationDateTime', '')
            if roi_obs_datetime:
                # DICOM DateTime format: YYYYMMDDHHMMSS.FFFFFF&ZZXX
                # Basic validation for minimum length and numeric characters
                if len(roi_obs_datetime) < 8 or not roi_obs_datetime[:8].isdigit():
                    result.add_warning(
                        f"RT ROI Observations Sequence item {i}: "
                        f"ROI Observation DateTime ({roi_obs_datetime}) should follow DICOM DateTime format (YYYYMMDDHHMMSS)"
                    )
