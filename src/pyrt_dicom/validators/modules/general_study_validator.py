"""General Study Module DICOM validation - PS3.3 C.7.2.1

Comprehensive validation for the General Study Module according to DICOM PS3.3 C.7.2.1.
Validates all Type 1, Type 2, Type 3, Type 1C, and Type 2C requirements with proper
conditional logic and sequence structure validation.
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class GeneralStudyValidator(BaseValidator):
    """Validator for DICOM General Study Module (PS3.3 C.7.2.1).

    Performs comprehensive validation including:
    - Type 1 required element presence (Study Instance UID)
    - Type 2 required element presence with empty value handling
    - Type 3 optional element format validation
    - Type 1C/2C conditional requirements with context-aware validation
    - Sequence structure validation (single vs multiple item constraints)
    - Person Identification Macro validation (Table 10-1)
    - Code Sequence Macro validation (Table 8.8-1)
    - Cross-field consistency (physician name/ID correspondence)
    - DICOM format compliance and enumerated value checking
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate General Study Module requirements on any pydicom Dataset.

        Performs comprehensive validation including:
        - Type 1 required element presence (Study Instance UID)
        - Type 2 required element presence with empty value handling
        - Type 3 optional element format and enumerated values
        - Sequence structure validation (single vs multiple item constraints)
        - Cross-field consistency (physician name/ID correspondence)
        - DICOM standard compliance (PS3.3 C.7.2.1)

        Args:
            dataset (Dataset): pydicom Dataset containing General Study Module data.
                             Must be a valid pydicom Dataset object.
            config (ValidationConfig | None): Optional validation configuration to control
                                             validation scope and behavior. If None, uses
                                             default configuration with all validations enabled.

        Returns:
            ValidationResult: Comprehensive validation result with structured errors and warnings.
                            Errors indicate DICOM standard violations that must be fixed.
                            Warnings indicate recommendations or potential issues.
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()

        # Validate Type 1 requirements (required and must be non-empty)
        GeneralStudyValidator._validate_type1_requirements(dataset, result)

        # Validate Type 2 requirements (required but can be empty)
        GeneralStudyValidator._validate_type2_requirements(dataset, result)

        # Validate sequence structures and constraints
        if config.validate_sequences:
            GeneralStudyValidator._validate_sequence_requirements(dataset, result)
            GeneralStudyValidator._validate_person_identification_sequences(dataset, result)

        # Validate cross-field consistency and correspondence requirements
        GeneralStudyValidator._validate_physician_correspondence(dataset, result)

        return result

    @staticmethod
    def _validate_type1_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 (required and non-empty) elements."""

        # Study Instance UID (0020,000D) Type 1
        if not hasattr(dataset, 'StudyInstanceUID') or not dataset.StudyInstanceUID:
            result.add_error(
                "Study Instance UID (0020,000D) is required and must be non-empty (Type 1). "
                "Provide a unique identifier for the Study using a valid UID format."
            )

    @staticmethod
    def _validate_type2_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 2 (required but can be empty) elements."""

        # Study Date (0008,0020) Type 2
        if not hasattr(dataset, 'StudyDate'):
            result.add_error(
                "Study Date (0008,0020) is required (Type 2). "
                "Provide the date the Study started in DICOM DA format (YYYYMMDD) or empty string."
            )

        # Study Time (0008,0030) Type 2
        if not hasattr(dataset, 'StudyTime'):
            result.add_error(
                "Study Time (0008,0030) is required (Type 2). "
                "Provide the time the Study started in DICOM TM format (HHMMSS) or empty string."
            )

        # Referring Physician's Name (0008,0090) Type 2
        if not hasattr(dataset, 'ReferringPhysicianName'):
            result.add_error(
                "Referring Physician's Name (0008,0090) is required (Type 2). "
                "Provide the name of the Patient's referring physician in DICOM PN format or empty string."
            )

        # Study ID (0020,0010) Type 2
        if not hasattr(dataset, 'StudyID'):
            result.add_error(
                "Study ID (0020,0010) is required (Type 2). "
                "Provide a user or equipment generated Study identifier or empty string."
            )

        # Accession Number (0008,0050) Type 2
        if not hasattr(dataset, 'AccessionNumber'):
            result.add_error(
                "Accession Number (0008,0050) is required (Type 2). "
                "Provide a departmental Information System generated number or empty string."
            )

    @staticmethod
    def _validate_physician_correspondence(dataset: Dataset, result: ValidationResult) -> None:
        """Validate correspondence between physician names and identification sequences."""

        # Consulting Physician correspondence
        consulting_name = getattr(dataset, 'ConsultingPhysicianName', None)
        consulting_seq = getattr(dataset, 'ConsultingPhysicianIdentificationSequence', [])

        if consulting_name and consulting_seq:
            # Handle both string and list formats for multi-value fields
            if isinstance(consulting_name, str):
                name_count = len(consulting_name.split('\\')) if consulting_name else 0
            else:
                name_count = len(consulting_name) if consulting_name else 0

            if len(consulting_seq) != name_count:
                result.add_warning(
                    f"Consulting Physician Identification Sequence (0008,009D) has {len(consulting_seq)} items "
                    f"but Consulting Physician Name (0008,009C) has {name_count} values. "
                    "Per DICOM PS3.3 C.7.2.1, the number and order should correspond when both are present."
                )

        # Physicians of Record correspondence
        record_name = getattr(dataset, 'PhysiciansOfRecord', None)
        record_seq = getattr(dataset, 'PhysiciansOfRecordIdentificationSequence', [])

        if record_name and record_seq:
            if isinstance(record_name, str):
                name_count = len(record_name.split('\\')) if record_name else 0
            else:
                name_count = len(record_name) if record_name else 0

            if len(record_seq) != name_count:
                result.add_warning(
                    f"Physician(s) of Record Identification Sequence (0008,1049) has {len(record_seq)} items "
                    f"but Physician(s) of Record (0008,1048) has {name_count} values. "
                    "Per DICOM PS3.3 C.7.2.1, the number and order should correspond when both are present."
                )

        # Physicians Reading Study correspondence
        reading_name = getattr(dataset, 'NameOfPhysiciansReadingStudy', None)
        reading_seq = getattr(dataset, 'PhysiciansReadingStudyIdentificationSequence', [])

        if reading_name and reading_seq:
            if isinstance(reading_name, str):
                name_count = len(reading_name.split('\\')) if reading_name else 0
            else:
                name_count = len(reading_name) if reading_name else 0

            if len(reading_seq) != name_count:
                result.add_warning(
                    f"Physician(s) Reading Study Identification Sequence (0008,1062) has {len(reading_seq)} items "
                    f"but Name of Physician(s) Reading Study (0008,1060) has {name_count} values. "
                    "Per DICOM PS3.3 C.7.2.1, the number and order should correspond when both are present."
                )

    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements and item count constraints."""

        # Referring Physician Identification Sequence validation (single item only)
        referring_phys_seq = getattr(dataset, 'ReferringPhysicianIdentificationSequence', [])
        if len(referring_phys_seq) > 1:
            result.add_error(
                "Referring Physician Identification Sequence (0008,0096) contains "
                f"{len(referring_phys_seq)} items but only a single Item is permitted. "
                "Per DICOM PS3.3 C.7.2.1, remove extra items to comply with the standard."
            )

        # Issuer of Accession Number Sequence validation (single item only)
        accession_issuer_seq = getattr(dataset, 'IssuerOfAccessionNumberSequence', [])
        if len(accession_issuer_seq) > 1:
            result.add_error(
                "Issuer of Accession Number Sequence (0008,0051) contains "
                f"{len(accession_issuer_seq)} items but only a single Item is permitted. "
                "Per DICOM PS3.3 C.7.2.1, remove extra items to comply with the standard."
            )

        # Requesting Service Code Sequence validation (single item only)
        requesting_service_seq = getattr(dataset, 'RequestingServiceCodeSequence', [])
        if len(requesting_service_seq) > 1:
            result.add_error(
                "Requesting Service Code Sequence (0032,1034) contains "
                f"{len(requesting_service_seq)} items but only a single Item is permitted. "
                "Per DICOM PS3.3 C.7.2.1, remove extra items to comply with the standard."
            )

        # Validate Code Sequence Macro requirements for sequences that use it
        GeneralStudyValidator._validate_code_sequences(dataset, result)

    @staticmethod
    def _validate_code_sequences(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Code Sequence Macro requirements for code sequences in the module."""

        # Requesting Service Code Sequence (uses Code Sequence Macro)
        requesting_service_seq = getattr(dataset, 'RequestingServiceCodeSequence', [])
        for i, item in enumerate(requesting_service_seq):
            item_prefix = f"Requesting Service Code Sequence (0032,1034) Item {i + 1}"
            GeneralStudyValidator._validate_code_sequence_item(item, item_prefix, result)

        # Procedure Code Sequence (uses Code Sequence Macro)
        procedure_code_seq = getattr(dataset, 'ProcedureCodeSequence', [])
        for i, item in enumerate(procedure_code_seq):
            item_prefix = f"Procedure Code Sequence (0008,1032) Item {i + 1}"
            GeneralStudyValidator._validate_code_sequence_item(item, item_prefix, result)

        # Reason For Performed Procedure Code Sequence (uses Code Sequence Macro)
        reason_code_seq = getattr(dataset, 'ReasonForPerformedProcedureCodeSequence', [])
        for i, item in enumerate(reason_code_seq):
            item_prefix = f"Reason For Performed Procedure Code Sequence (0040,1012) Item {i + 1}"
            GeneralStudyValidator._validate_code_sequence_item(item, item_prefix, result)

    @staticmethod
    def _validate_person_identification_sequences(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Person Identification Macro (Table 10-1) requirements in physician identification sequences.

        Validates all physician identification sequences according to DICOM PS3.3 Table 10-1
        Person Identification Macro Attributes, ensuring proper structure and required elements.
        """

        # List of physician identification sequences to validate
        sequences_to_validate = [
            ('ReferringPhysicianIdentificationSequence', 'Referring Physician Identification Sequence (0008,0096)'),
            ('ConsultingPhysicianIdentificationSequence', 'Consulting Physician Identification Sequence (0008,009D)'),
            ('PhysiciansOfRecordIdentificationSequence', 'Physician(s) of Record Identification Sequence (0008,1049)'),
            ('PhysiciansReadingStudyIdentificationSequence', 'Physician(s) Reading Study Identification Sequence (0008,1062)')
        ]

        for seq_attr, seq_name in sequences_to_validate:
            sequence = getattr(dataset, seq_attr, [])
            for i, item in enumerate(sequence):
                GeneralStudyValidator._validate_person_identification_item(item, seq_name, i, result)

    @staticmethod
    def _validate_person_identification_item(item: Dataset, seq_name: str, item_index: int, result: ValidationResult) -> None:
        """Validate a single Person Identification Macro item (Table 10-1).

        Validates all requirements from DICOM PS3.3 Table 10-1 Person Identification Macro
        including Type 1 and Type 1C requirements with proper error guidance.
        """

        item_prefix = f"{seq_name} Item {item_index + 1}"

        # Validate Person Identification Code Sequence (0040,1101) Type 1
        person_id_code_seq = getattr(item, 'PersonIdentificationCodeSequence', [])
        if not person_id_code_seq:
            result.add_error(
                f"{item_prefix}: Person Identification Code Sequence (0040,1101) is required (Type 1). "
                "Add at least one code sequence item using the Code Sequence Macro (Table 8.8-1) "
                "to identify the person according to DICOM PS3.3 Table 10-1."
            )
        else:
            # Validate each code sequence item (Table 8.8-1)
            for j, code_item in enumerate(person_id_code_seq):
                code_prefix = f"{item_prefix} Person ID Code {j + 1}"
                GeneralStudyValidator._validate_code_sequence_item(code_item, code_prefix, result)

        # Validate Type 1C requirement: Institution Name OR Institution Code Sequence must be present
        institution_name = getattr(item, 'InstitutionName', None)
        institution_code_seq = getattr(item, 'InstitutionCodeSequence', [])

        if not institution_name and not institution_code_seq:
            result.add_error(
                f"{item_prefix}: Either Institution Name (0008,0080) or Institution Code Sequence (0008,0082) "
                "must be present (Type 1C requirement). Per DICOM PS3.3 Table 10-1, provide either the "
                "institution name as text or a coded representation of the institution."
            )

        # Validate Institution Code Sequence if present
        if institution_code_seq:
            if len(institution_code_seq) > 1:
                result.add_error(
                    f"{item_prefix}: Institution Code Sequence (0008,0082) contains "
                    f"{len(institution_code_seq)} items but only a single Item shall be included. "
                    "Per DICOM PS3.3 Table 10-1, remove extra items to comply with the standard."
                )
            for j, code_item in enumerate(institution_code_seq):
                code_prefix = f"{item_prefix} Institution Code {j + 1}"
                GeneralStudyValidator._validate_code_sequence_item(code_item, code_prefix, result)

    @staticmethod
    def _validate_code_sequence_item(code_item: Dataset, item_prefix: str, result: ValidationResult) -> None:
        """Validate a Code Sequence Macro item (Table 8.8-1).

        Validates all requirements from DICOM PS3.3 Table 8.8-1 Code Sequence Macro
        including Type 1 and Type 1C requirements with proper error guidance.
        """

        # Check Code Meaning (Type 1)
        if not hasattr(code_item, 'CodeMeaning') or not code_item.CodeMeaning:
            result.add_error(
                f"{item_prefix}: Code Meaning (0008,0104) is required and must be non-empty (Type 1). "
                "Provide a human-readable meaning of the code according to DICOM PS3.3 Table 8.8-1."
            )

        # Check Type 1C requirements for code values (at least one must be present)
        code_value = getattr(code_item, 'CodeValue', None)
        long_code_value = getattr(code_item, 'LongCodeValue', None)
        urn_code_value = getattr(code_item, 'URNCodeValue', None)

        if not any([code_value, long_code_value, urn_code_value]):
            result.add_error(
                f"{item_prefix}: At least one of Code Value (0008,0100), Long Code Value (0008,0119), "
                "or URN Code Value (0008,0120) must be present (Type 1C requirement). "
                "Per DICOM PS3.3 Table 8.8-1, provide the actual code identifier."
            )

        # Check Coding Scheme Designator (Type 1C - required if Code Value or Long Code Value is present)
        coding_scheme_designator = getattr(code_item, 'CodingSchemeDesignator', None)
        if (code_value or long_code_value) and not coding_scheme_designator:
            result.add_error(
                f"{item_prefix}: Coding Scheme Designator (0008,0102) is required when "
                "Code Value or Long Code Value is present (Type 1C requirement). "
                "Per DICOM PS3.3 Table 8.8-1, specify the coding scheme that defines the code."
            )
