"""Test RTBeamsValidator functionality.

Tests comprehensive validation of RT Beams Module according to DICOM PS3.3 C.8.8.14.
All tests use datasets generated from modules via to_dataset() method following
the new composition-based architecture patterns.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.modules import RTBeamsModule
from pyrt_dicom.validators.modules.rt_beams_validator import RTBeamsValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums import BeamType, RadiationType
from pyrt_dicom.enums.rt_enums import (
    RTBeamLimitingDeviceType,
    PrimaryDosimeterUnit, 
    EnhancedRTBeamLimitingDeviceDefinitionFlag
)


class TestRTBeamsValidator:
    """Test RTBeamsValidator comprehensive validation logic."""
    
    def test_validate_method_signature(self):
        """Test validator method signature and return type."""
        # Create minimal valid dataset
        dataset = Dataset()
        dataset.BeamSequence = []
        
        # Test with default config
        result = RTBeamsValidator.validate(dataset)
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
        
        # Test with custom config
        config = ValidationConfig()
        result = RTBeamsValidator.validate(dataset, config)
        assert isinstance(result, ValidationResult)
    
    def test_empty_beam_sequence_validation(self):
        """Test validation of empty beam sequence."""
        dataset = Dataset()
        dataset.BeamSequence = []
        
        result = RTBeamsValidator.validate(dataset)
        
        # Should have error for empty beam sequence
        assert len(result.errors) > 0
        assert any('must contain one or more Items' in error for error in result.errors)
    
    def test_missing_beam_sequence_validation(self):
        """Test validation when Beam Sequence is completely missing."""
        dataset = Dataset()
        # No BeamSequence attribute
        
        result = RTBeamsValidator.validate(dataset)
        
        # Should have error for missing beam sequence
        assert len(result.errors) > 0
        assert any('Beam Sequence (300A,00B0) is required (Type 1)' in error for error in result.errors)
    
    def test_valid_minimal_beam_validation(self):
        """Test validation of minimally valid beam."""
        # Create minimal valid beam using module
        control_points = [
            RTBeamsModule.create_control_point_item(
                control_point_index=0,
                cumulative_meterset_weight=0.0
            ),
            RTBeamsModule.create_control_point_item(
                control_point_index=1,
                cumulative_meterset_weight=1.0
            )
        ]
        
        beam_item = RTBeamsModule.create_beam_item(
            beam_number=1,
            beam_type=BeamType.STATIC,
            treatment_machine_name='TrueBeam',
            radiation_type=RadiationType.PHOTON,
            number_of_wedges=0,
            number_of_compensators=0,
            number_of_boli=0,
            number_of_blocks=0,
            number_of_control_points=2,
            control_point_sequence=control_points,
            final_cumulative_meterset_weight=1.0,
            beam_limiting_device_sequence=[
                RTBeamsModule.create_beam_limiting_device_item(
                    rt_beam_limiting_device_type=RTBeamLimitingDeviceType.X,
                    number_of_leaf_jaw_pairs=1
                )
            ]
        )
        
        beams = RTBeamsModule.from_required_elements(beam_sequence=[beam_item])
        dataset = beams.to_dataset()
        
        result = RTBeamsValidator.validate(dataset)
        
        # Should pass validation with no errors
        assert len(result.errors) == 0
    
    def test_beam_type_1_element_validation(self):
        """Test validation of Type 1 (required) elements."""
        dataset = Dataset()
        beam_item = Dataset()
        # Missing required Type 1 elements
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        # Should have errors for missing Type 1 elements
        expected_missing_elements = [
            'BeamNumber', 'BeamType', 'NumberOfWedges', 'NumberOfCompensators',
            'NumberOfBoli', 'NumberOfBlocks', 'NumberOfControlPoints', 'ControlPointSequence'
        ]
        
        for element in expected_missing_elements:
            assert any(f'{element}' in error and 'is required (Type 1)' in error 
                      for error in result.errors), f'Missing error for {element}'
    
    def test_beam_type_2_element_validation(self):
        """Test validation of Type 2 (required but can be empty) elements."""
        dataset = Dataset()
        beam_item = Dataset()
        # Add Type 1 elements but missing Type 2
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 1
        beam_item.ControlPointSequence = []
        # Missing TreatmentMachineName and RadiationType (Type 2)
        
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        # Should have errors for missing Type 2 elements
        assert any('TreatmentMachineName' in error and 'is required (Type 2)' in error 
                  for error in result.errors)
        assert any('RadiationType' in error and 'is required (Type 2)' in error 
                  for error in result.errors)
    
    def test_enhanced_device_flag_conditional_validation(self):
        """Test Enhanced RT Beam Limiting Device Definition Flag conditional logic."""
        # Test case 1: Flag absent/NO requires Beam Limiting Device Sequence
        control_points = [RTBeamsModule.create_control_point_item(0, 0.0)]
        
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 1
        beam_item.ControlPointSequence = control_points
        beam_item.EnhancedRTBeamLimitingDeviceDefinitionFlag = 'NO'
        # Missing BeamLimitingDeviceSequence
        
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Beam Limiting Device Sequence (300A,00B6) is required when' in error 
                  for error in result.errors)
        
        # Test case 2: Flag YES requires Enhanced RT Beam Limiting Device Sequence
        beam_item.EnhancedRTBeamLimitingDeviceDefinitionFlag = 'YES'
        # Still missing EnhancedRTBeamLimitingDeviceSequence
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Enhanced RT Beam Limiting Device Sequence (3008,00A1) is required when' in error 
                  for error in result.errors)
    
    def test_number_based_sequence_requirements(self):
        """Test number-based conditional sequence requirements."""
        control_points = [RTBeamsModule.create_control_point_item(0, 0.0)]
        
        # Test wedge sequence requirement
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 2  # Non-zero requires WedgeSequence
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 1
        beam_item.ControlPointSequence = control_points
        beam_item.BeamLimitingDeviceSequence = []
        # Missing WedgeSequence
        
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Wedge Sequence (300A,00D1) is required when' in error and 
                  'Number of Wedges (2) is non-zero' in error for error in result.errors)
        
        # Test compensator sequence requirement
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 1  # Non-zero requires CompensatorSequence
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Compensator Sequence (300A,00E3) is required when' in error and
                  'Number of Compensators (1) is non-zero' in error for error in result.errors)
        
        # Test bolus sequence requirement
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 1  # Non-zero requires ReferencedBolusSequence
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Referenced Bolus Sequence (300C,00B0) is required when' in error and
                  'Number of Boli (1) is non-zero' in error for error in result.errors)
        
        # Test block sequence requirement
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 1  # Non-zero requires BlockSequence
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Block Sequence (300A,00F4) is required when' in error and
                  'Number of Blocks (1) is non-zero' in error for error in result.errors)
    
    def test_final_cumulative_meterset_weight_validation(self):
        """Test Final Cumulative Meterset Weight conditional requirements."""
        control_points = [
            RTBeamsModule.create_control_point_item(0, 0.0),
            RTBeamsModule.create_control_point_item(1, 1.0)  # Non-null cumulative weight
        ]
        
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 2
        beam_item.ControlPointSequence = control_points
        beam_item.BeamLimitingDeviceSequence = []
        # Missing FinalCumulativeMetersetWeight when control points have non-null weights
        
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Final Cumulative Meterset Weight (300A,010E) is required when' in error and
                  'Cumulative Meterset Weight is non-null in Control Points' in error
                  for error in result.errors)
    
    def test_beam_limiting_device_mlc_validation(self):
        """Test beam limiting device MLC conditional requirements."""
        control_points = [RTBeamsModule.create_control_point_item(0, 0.0)]
        
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 1
        beam_item.ControlPointSequence = control_points
        
        # Create beam limiting device without leaf position boundaries for MLCX
        bld_item = Dataset()
        bld_item.RTBeamLimitingDeviceType = 'MLCX'
        bld_item.NumberOfLeafJawPairs = 60
        # Missing LeafPositionBoundaries for MLCX
        
        beam_item.BeamLimitingDeviceSequence = [bld_item]
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Leaf Position Boundaries (300A,00BE) is required when' in error and
                  'RT Beam Limiting Device Type is MLCX' in error for error in result.errors)
    
    def test_material_id_conditional_requirements(self):
        """Test Material ID conditional requirements for compensators and blocks."""
        # Create beam with compensator that has zero-length material ID
        control_points = [RTBeamsModule.create_control_point_item(0, 0.0)]
        
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 1
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 1
        beam_item.ControlPointSequence = control_points
        beam_item.BeamLimitingDeviceSequence = []
        
        # Compensator with zero-length Material ID should require transmission data
        comp_item = Dataset()
        comp_item.CompensatorRows = 10
        comp_item.CompensatorColumns = 10
        comp_item.MaterialID = ''  # Zero-length
        # Missing CompensatorTransmissionData
        
        beam_item.CompensatorSequence = [comp_item]
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Compensator Transmission Data (300A,00EB) is required when Material ID is zero-length' in error
                  for error in result.errors)
        
        # Test block with non-zero Material ID should require thickness data
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBlocks = 1
        beam_item.CompensatorSequence = []
        
        block_item = Dataset()
        block_item.BlockNumber = 1
        block_item.BlockType = 'SHIELDING'
        block_item.MaterialID = 'LEAD'  # Non-zero length
        # Missing BlockThickness
        
        beam_item.BlockSequence = [block_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Block Thickness (300A,0100) is required when Material ID is non-zero length' in error
                  for error in result.errors)
    
    def test_control_point_sequence_validation(self):
        """Test control point sequence validation."""
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 2  # Declare 2 control points
        beam_item.BeamLimitingDeviceSequence = []
        
        # But only provide 1 control point
        control_points = [RTBeamsModule.create_control_point_item(0, 0.0)]
        beam_item.ControlPointSequence = control_points
        
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Control Point Sequence contains 1 items but Number of Control Points declares 2' in error
                  for error in result.errors)
    
    def test_control_point_index_validation(self):
        """Test control point index sequential validation."""
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 2
        beam_item.BeamLimitingDeviceSequence = []
        
        # Create control points with wrong indices
        cp1 = Dataset()
        cp1.ControlPointIndex = 0
        cp1.CumulativeMetersetWeight = 0.0
        
        cp2 = Dataset()
        cp2.ControlPointIndex = 5  # Should be 1, not 5
        cp2.CumulativeMetersetWeight = 1.0
        
        beam_item.ControlPointSequence = [cp1, cp2]
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Control Point Index (5) should be 1 (sequential)' in error
                  for error in result.errors)
    
    def test_beam_number_uniqueness_validation(self):
        """Test beam number uniqueness validation."""
        control_points = [RTBeamsModule.create_control_point_item(0, 0.0)]
        
        # Create two beams with the same beam number
        beam1 = Dataset()
        beam1.BeamNumber = 1  # Same number
        beam1.BeamType = 'STATIC'
        beam1.TreatmentMachineName = 'Test1'
        beam1.RadiationType = 'PHOTON'
        beam1.NumberOfWedges = 0
        beam1.NumberOfCompensators = 0
        beam1.NumberOfBoli = 0
        beam1.NumberOfBlocks = 0
        beam1.NumberOfControlPoints = 1
        beam1.ControlPointSequence = control_points
        beam1.BeamLimitingDeviceSequence = []
        
        beam2 = Dataset()
        beam2.BeamNumber = 1  # Same number (should be unique)
        beam2.BeamType = 'DYNAMIC'
        beam2.TreatmentMachineName = 'Test2'
        beam2.RadiationType = 'ELECTRON'
        beam2.NumberOfWedges = 0
        beam2.NumberOfCompensators = 0
        beam2.NumberOfBoli = 0
        beam2.NumberOfBlocks = 0
        beam2.NumberOfControlPoints = 1
        beam2.ControlPointSequence = control_points
        beam2.BeamLimitingDeviceSequence = []
        
        dataset = Dataset()
        dataset.BeamSequence = [beam1, beam2]
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Beam Number (1) must be unique within the RT Plan' in error
                  for error in result.errors)
    
    def test_enumerated_values_validation(self):
        """Test enumerated values validation."""
        control_points = [RTBeamsModule.create_control_point_item(0, 0.0)]
        
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'INVALID_TYPE'  # Invalid enumerated value
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'INVALID_RADIATION'  # Invalid enumerated value
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 1
        beam_item.ControlPointSequence = control_points
        beam_item.BeamLimitingDeviceSequence = []
        
        dataset.BeamSequence = [beam_item]
        
        config = ValidationConfig(check_enumerated_values=True)
        result = RTBeamsValidator.validate(dataset, config)
        
        assert any('Beam Type (300A,00C4)' in error and 'INVALID_TYPE' in error
                  for error in result.errors)
        assert any('Radiation Type (300A,00C6)' in error and 'INVALID_RADIATION' in error
                  for error in result.errors)
    
    def test_validation_configuration_flags(self):
        """Test different validation configuration flags."""
        # Create dataset with various issues
        control_points = [RTBeamsModule.create_control_point_item(0, 0.0)]
        
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'INVALID_TYPE'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 1  # Should require WedgeSequence
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 1
        beam_item.ControlPointSequence = control_points
        beam_item.BeamLimitingDeviceSequence = []
        
        dataset.BeamSequence = [beam_item]
        
        # Test with enumerated values disabled
        config = ValidationConfig(check_enumerated_values=False)
        result = RTBeamsValidator.validate(dataset, config)
        # Should not have enumerated value errors
        assert not any('INVALID_TYPE' in error for error in result.errors)
        
        # Test with conditional requirements disabled
        config = ValidationConfig(validate_conditional_requirements=False)
        result = RTBeamsValidator.validate(dataset, config)
        # Should not have conditional requirement errors
        assert not any('Wedge Sequence (300A,00D1) is required' in error for error in result.errors)
    
    def test_final_weight_consistency_warning(self):
        """Test final cumulative meterset weight consistency warning."""
        control_points = [
            RTBeamsModule.create_control_point_item(0, 0.0),
            RTBeamsModule.create_control_point_item(1, 0.8)  # Last control point weight
        ]
        
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 2
        beam_item.ControlPointSequence = control_points
        beam_item.BeamLimitingDeviceSequence = []
        beam_item.FinalCumulativeMetersetWeight = 1.0  # Different from last control point (0.8)
        
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Final Cumulative Meterset Weight (1.0) should match last control point weight (0.8)' in warning
                  for warning in result.warnings)
    
    def test_complex_beam_validation_success(self):
        """Test validation of complex beam with all elements passes."""
        # Create a complex beam with wedges, compensators, blocks, etc.
        control_points = [
            RTBeamsModule.create_control_point_item(0, 0.0),
            RTBeamsModule.create_control_point_item(1, 1.0)
        ]
        
        wedge_item = RTBeamsModule.create_wedge_item(
            wedge_number=1,
            wedge_type='STANDARD',
            wedge_angle=15.0,
            wedge_factor=0.75,
            wedge_orientation=90.0
        )
        
        compensator_item = RTBeamsModule.create_compensator_item(
            compensator_number=1,
            material_id='LEAD',  # Non-zero, requires thickness data
            compensator_rows=10,
            compensator_columns=10,
            compensator_thickness_data=[1.0, 2.0, 3.0]  # Required for non-zero material ID
        )
        
        block_item = RTBeamsModule.create_block_item(
            block_number=1,
            block_type='SHIELDING',
            block_divergence='PRESENT',
            material_id='',  # Zero-length, requires transmission data
            block_number_of_points=4,
            block_data=[-10.0, -10.0, 10.0, -10.0, 10.0, 10.0, -10.0, 10.0],
            block_transmission=0.05  # Required for zero-length material ID
        )
        
        beam_item = RTBeamsModule.create_beam_item(
            beam_number=1,
            beam_type=BeamType.STATIC,
            treatment_machine_name='TrueBeam',
            radiation_type=RadiationType.PHOTON,
            number_of_wedges=1,
            number_of_compensators=1,
            number_of_boli=0,
            number_of_blocks=1,
            number_of_control_points=2,
            control_point_sequence=control_points,
            final_cumulative_meterset_weight=1.0,
            beam_limiting_device_sequence=[
                RTBeamsModule.create_beam_limiting_device_item(
                    rt_beam_limiting_device_type=RTBeamLimitingDeviceType.X,
                    number_of_leaf_jaw_pairs=1
                )
            ],
            wedge_sequence=[wedge_item],
            compensator_sequence=[compensator_item],
            block_sequence=[block_item]
        )
        
        beams = RTBeamsModule.from_required_elements(beam_sequence=[beam_item])
        dataset = beams.to_dataset()
        
        result = RTBeamsValidator.validate(dataset)
        
        # Complex but valid beam should pass validation
        assert len(result.errors) == 0, f'Unexpected validation errors: {result.errors}'