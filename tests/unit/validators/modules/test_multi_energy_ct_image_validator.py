"""Tests for Multi-energy CT Image Module Validator - DICOM PS3.3 C.8.2.2"""

import pytest
from pydicom import Dataset
from src.pyrt_dicom.validators.modules.multi_energy_ct_image_validator import MultiEnergyCTImageValidator
from src.pyrt_dicom.validators.modules.base_validator import ValidationConfig
from src.pyrt_dicom.validators.validation_result import ValidationResult
from src.pyrt_dicom.modules.modules.multi_energy_ct_image_module import MultiEnergyCTImageModule
from src.pyrt_dicom.enums.contrast_ct_enums import MultiEnergySourceTechnique, MultiEnergyDetectorType


class TestMultiEnergyCTImageValidator:
    """Test suite for Multi-energy CT Image Module Validator."""

    def setup_method(self):
        """Set up test fixtures for each test method."""
        self.config = ValidationConfig()
        self.valid_dataset = self._create_valid_multi_energy_dataset()

    def _create_valid_multi_energy_dataset(self) -> Dataset:
        """Create a valid Multi-energy CT Image module dataset for testing."""
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000"
                    )
                ],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    ),
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=2,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        
        return module.to_dataset()

    def test_validate_returns_validation_result(self):
        """Test that validate method returns ValidationResult instance."""
        result = MultiEnergyCTImageValidator.validate(self.valid_dataset, self.config)
        assert isinstance(result, ValidationResult)

    def test_valid_dataset_passes_validation(self):
        """Test that a valid Multi-energy CT Image dataset passes validation without errors."""
        result = MultiEnergyCTImageValidator.validate(self.valid_dataset, self.config)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    # Type 1 Required Element Tests

    def test_missing_multi_energy_ct_acquisition_sequence_fails(self):
        """Test that missing Multi-energy CT Acquisition Sequence generates error."""
        dataset = Dataset()  # Empty dataset
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        assert len(result.errors) >= 1
        assert any(
            "Multi-energy CT Acquisition Sequence (0018,9362) is required (Type 1)" in error
            for error in result.errors
        )

    def test_empty_multi_energy_ct_acquisition_sequence_fails(self):
        """Test that empty Multi-energy CT Acquisition Sequence generates error."""
        dataset = Dataset()
        dataset.add_new((0x0018, 0x9362), 'SQ', [])  # Empty sequence
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        assert len(result.errors) >= 1
        assert any(
            "Multi-energy CT Acquisition Sequence (0018,9362) must contain at least one Item" in error
            for error in result.errors
        )

    # Sequence Structure Validation Tests

    def test_missing_x_ray_source_sequence_fails(self):
        """Test that missing X-Ray Source Sequence in acquisition item generates error."""
        acquisition_item = Dataset()
        # Missing x_ray_source_sequence
        acquisition_item.add_new((0x0018, 0x936F), 'SQ', [])  # Empty detector sequence
        acquisition_item.add_new((0x0018, 0x9379), 'SQ', [])  # Empty path sequence
        
        dataset = Dataset()
        dataset.add_new((0x0018, 0x9362), 'SQ', [acquisition_item])
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        assert len(result.errors) >= 1
        assert any(
            "Multi-energy CT X-Ray Source Sequence (0018,9365) is required" in error
            for error in result.errors
        )

    def test_missing_x_ray_detector_sequence_fails(self):
        """Test that missing X-Ray Detector Sequence in acquisition item generates error."""
        source_item = MultiEnergyCTImageModule.create_x_ray_source_item(
            x_ray_source_index=1,
            x_ray_source_id="SOURCE_001",
            multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
            source_start_datetime="20240101120000.000000",
            source_end_datetime="20240101120030.000000"
        )
        
        acquisition_item = Dataset()
        acquisition_item.add_new((0x0018, 0x9365), 'SQ', [source_item])
        # Missing x_ray_detector_sequence
        acquisition_item.add_new((0x0018, 0x9379), 'SQ', [])  # Empty path sequence
        
        dataset = Dataset()
        dataset.add_new((0x0018, 0x9362), 'SQ', [acquisition_item])
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        assert len(result.errors) >= 1
        assert any(
            "Multi-energy CT X-Ray Detector Sequence (0018,936F) is required" in error
            for error in result.errors
        )

    def test_missing_path_sequence_fails(self):
        """Test that missing Path Sequence in acquisition item generates error."""
        source_item = MultiEnergyCTImageModule.create_x_ray_source_item(
            x_ray_source_index=1,
            x_ray_source_id="SOURCE_001",
            multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
            source_start_datetime="20240101120000.000000",
            source_end_datetime="20240101120030.000000"
        )
        
        detector_item = MultiEnergyCTImageModule.create_x_ray_detector_item(
            x_ray_detector_index=1,
            x_ray_detector_id="DETECTOR_001",
            multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
        )
        
        acquisition_item = Dataset()
        acquisition_item.add_new((0x0018, 0x9365), 'SQ', [source_item])
        acquisition_item.add_new((0x0018, 0x936F), 'SQ', [detector_item])
        # Missing path_sequence
        
        dataset = Dataset()
        dataset.add_new((0x0018, 0x9362), 'SQ', [acquisition_item])
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        assert len(result.errors) >= 1
        assert any(
            "Multi-energy CT Path Sequence (0018,9379) is required" in error
            for error in result.errors
        )

    def test_insufficient_path_sequence_items_fails(self):
        """Test that Path Sequence with less than 2 items generates error."""
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000"
                    )
                ],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
                    )
                ],
                path_sequence=[
                    # Only one path item - should require 2 or more
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        dataset = module.to_dataset()
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        assert len(result.errors) >= 1
        assert any(
            "Multi-energy CT Path Sequence (0018,9379) must contain two or more Items" in error
            for error in result.errors
        )

    # X-Ray Source Sequence Element Tests

    @pytest.mark.parametrize(
        "missing_tag,expected_error_text",
        [
            ((0x0018, 0x9366), "X-Ray Source Index (0018,9366) is required"),
            ((0x0018, 0x9367), "X-Ray Source ID (0018,9367) is required"),
            ((0x0018, 0x9368), "Multi-energy Source Technique (0018,9368) is required"),
            ((0x0018, 0x9369), "Source Start DateTime (0018,9369) is required"),
            ((0x0018, 0x936A), "Source End DateTime (0018,936A) is required"),
        ],
    )
    def test_missing_x_ray_source_required_elements_fails(self, missing_tag, expected_error_text):
        """Test that missing required elements in X-Ray Source Sequence generate errors."""
        # Create a source item with all required elements
        source_item = MultiEnergyCTImageModule.create_x_ray_source_item(
            x_ray_source_index=1,
            x_ray_source_id="SOURCE_001",
            multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
            source_start_datetime="20240101120000.000000",
            source_end_datetime="20240101120030.000000"
        )
        
        # Remove the specific element to test
        if missing_tag in source_item:
            del source_item[missing_tag]
        
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[source_item],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    ),
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=2,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        dataset = module.to_dataset()
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        assert len(result.errors) >= 1
        assert any(expected_error_text in error for error in result.errors)

    # X-Ray Detector Sequence Element Tests

    @pytest.mark.parametrize(
        "missing_tag,expected_error_text",
        [
            ((0x0018, 0x9370), "X-Ray Detector Index (0018,9370) is required"),
            ((0x0018, 0x9371), "X-Ray Detector ID (0018,9371) is required"),
            ((0x0018, 0x9372), "Multi-energy Detector Type (0018,9372) is required"),
        ],
    )
    def test_missing_x_ray_detector_required_elements_fails(self, missing_tag, expected_error_text):
        """Test that missing required elements in X-Ray Detector Sequence generate errors."""
        # Create a detector item with all required elements
        detector_item = MultiEnergyCTImageModule.create_x_ray_detector_item(
            x_ray_detector_index=1,
            x_ray_detector_id="DETECTOR_001",
            multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
        )
        
        # Remove the specific element to test
        if missing_tag in detector_item:
            del detector_item[missing_tag]
        
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000"
                    )
                ],
                x_ray_detector_sequence=[detector_item],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    ),
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=2,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        dataset = module.to_dataset()
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        assert len(result.errors) >= 1
        assert any(expected_error_text in error for error in result.errors)

    # Path Sequence Element Tests

    @pytest.mark.parametrize(
        "missing_tag,expected_error_text",
        [
            ((0x0018, 0x937A), "Multi-energy CT Path Index (0018,937A) is required"),
            ((0x0018, 0x9377), "Referenced X-Ray Source Index (0018,9377) is required"),
            ((0x0018, 0x9376), "Referenced X-Ray Detector Index (0018,9376) is required"),
        ],
    )
    def test_missing_path_required_elements_fails(self, missing_tag, expected_error_text):
        """Test that missing required elements in Path Sequence generate errors."""
        # Create path items with all required elements
        path_item1 = MultiEnergyCTImageModule.create_path_item(
            path_index=1,
            referenced_x_ray_source_index=1,
            referenced_x_ray_detector_index=1
        )
        path_item2 = MultiEnergyCTImageModule.create_path_item(
            path_index=2,
            referenced_x_ray_source_index=1,
            referenced_x_ray_detector_index=1
        )
        
        # Remove the specific element from first path item to test
        if missing_tag in path_item1:
            del path_item1[missing_tag]
        
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000"
                    )
                ],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
                    )
                ],
                path_sequence=[path_item1, path_item2]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        dataset = module.to_dataset()
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        assert len(result.errors) >= 1
        assert any(expected_error_text in error for error in result.errors)

    # Type 1C Conditional Logic Tests

    def test_switching_phase_number_required_for_switching_source(self):
        """Test that Switching Phase Number is required when technique is SWITCHING_SOURCE."""
        # Create source with SWITCHING_SOURCE but no switching phase number
        source_item = Dataset()
        source_item.add_new((0x0018, 0x9366), 'US', 1)  # XRaySourceIndex
        source_item.add_new((0x0018, 0x9367), 'UC', 'SOURCE_001')  # XRaySourceID
        source_item.add_new((0x0018, 0x9368), 'CS', 'SWITCHING_SOURCE')  # MultiEnergySourceTechnique
        source_item.add_new((0x0018, 0x9369), 'DT', '20240101120000.000000')  # SourceStartDateTime
        source_item.add_new((0x0018, 0x936A), 'DT', '20240101120030.000000')  # SourceEndDateTime
        # Missing (0x0018, 0x936B) SwitchingPhaseNumber
        
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[source_item],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    ),
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=2,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        dataset = module.to_dataset()
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        assert len(result.errors) >= 1
        assert any(
            "Switching Phase Number (0018,936B) is required" in error and
            "when Multi-energy Source Technique is SWITCHING_SOURCE" in error
            for error in result.errors
        )

    def test_switching_phase_number_present_for_switching_source_passes(self):
        """Test that Switching Phase Number present for SWITCHING_SOURCE passes."""
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.SWITCHING_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000",
                        switching_phase_number=1  # Required for SWITCHING_SOURCE
                    )
                ],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    ),
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=2,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        dataset = module.to_dataset()
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        switching_phase_errors = [
            error for error in result.errors 
            if "Switching Phase Number" in error
        ]
        assert len(switching_phase_errors) == 0

    def test_energy_ranges_required_for_photon_counting(self):
        """Test that energy ranges are required when detector type is PHOTON_COUNTING."""
        # Create detector with PHOTON_COUNTING but no energy ranges
        detector_item = Dataset()
        detector_item.add_new((0x0018, 0x9370), 'US', 1)  # XRayDetectorIndex
        detector_item.add_new((0x0018, 0x9371), 'UC', 'DETECTOR_001')  # XRayDetectorID
        detector_item.add_new((0x0018, 0x9372), 'CS', 'PHOTON_COUNTING')  # MultiEnergyDetectorType
        # Missing (0x0018, 0x9374) NominalMaxEnergy and (0x0018, 0x9375) NominalMinEnergy
        
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000"
                    )
                ],
                x_ray_detector_sequence=[detector_item],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    ),
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=2,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        dataset = module.to_dataset()
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        assert len(result.errors) >= 2
        assert any(
            "Nominal Max Energy (0018,9374) is required" in error and
            "when Multi-energy Detector Type is PHOTON_COUNTING" in error
            for error in result.errors
        )
        assert any(
            "Nominal Min Energy (0018,9375) is required" in error and
            "when Multi-energy Detector Type is PHOTON_COUNTING" in error
            for error in result.errors
        )

    def test_energy_ranges_present_for_photon_counting_passes(self):
        """Test that energy ranges present for PHOTON_COUNTING passes."""
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000"
                    )
                ],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.PHOTON_COUNTING,
                        nominal_max_energy=120.0,  # Required for PHOTON_COUNTING
                        nominal_min_energy=20.0    # Required for PHOTON_COUNTING
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    ),
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=2,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        dataset = module.to_dataset()
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        energy_errors = [
            error for error in result.errors 
            if "Nominal Max Energy" in error or "Nominal Min Energy" in error
        ]
        assert len(energy_errors) == 0

    # Reference Validation Tests

    def test_invalid_source_index_reference_fails(self):
        """Test that invalid source index reference in path sequence generates error."""
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,  # Available source index
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000"
                    )
                ],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=2,  # Invalid - references non-existent source index 2
                        referenced_x_ray_detector_index=1
                    ),
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=2,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        dataset = module.to_dataset()
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        assert len(result.errors) >= 1
        assert any(
            "Referenced X-Ray Source Index 2" in error and
            "does not match any X-Ray Source Index" in error
            for error in result.errors
        )

    def test_invalid_detector_index_reference_fails(self):
        """Test that invalid detector index reference in path sequence generates error."""
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000"
                    )
                ],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,  # Available detector index
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=2  # Invalid - references non-existent detector index 2
                    ),
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=2,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        dataset = module.to_dataset()
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        assert len(result.errors) >= 1
        assert any(
            "Referenced X-Ray Detector Index 2" in error and
            "does not match any X-Ray Detector Index" in error
            for error in result.errors
        )

    # Enumerated Value Validation Tests

    @pytest.mark.parametrize(
        "invalid_technique,expected_valid_techniques",
        [
            ("INVALID_TECHNIQUE", ["SWITCHING_SOURCE", "CONSTANT_SOURCE"]),
        ],
    )
    def test_invalid_source_technique_generates_warning(self, invalid_technique, expected_valid_techniques):
        """Test that invalid source technique values generate warnings."""
        # Create source with invalid technique
        source_item = Dataset()
        source_item.add_new((0x0018, 0x9366), 'US', 1)  # XRaySourceIndex
        source_item.add_new((0x0018, 0x9367), 'UC', 'SOURCE_001')  # XRaySourceID
        source_item.add_new((0x0018, 0x9368), 'CS', invalid_technique)  # Invalid technique
        source_item.add_new((0x0018, 0x9369), 'DT', '20240101120000.000000')  # SourceStartDateTime
        source_item.add_new((0x0018, 0x936A), 'DT', '20240101120030.000000')  # SourceEndDateTime
        
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[source_item],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    ),
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=2,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        dataset = module.to_dataset()
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        assert len(result.warnings) >= 1
        assert any(
            "Multi-energy Source Technique" in warning
            for warning in result.warnings
        )

    @pytest.mark.parametrize(
        "invalid_detector_type,expected_valid_types",
        [
            ("INVALID_DETECTOR", ["INTEGRATING", "MULTILAYER", "PHOTON_COUNTING"]),
        ],
    )
    def test_invalid_detector_type_generates_warning(self, invalid_detector_type, expected_valid_types):
        """Test that invalid detector type values generate warnings."""
        # Create detector with invalid type
        detector_item = Dataset()
        detector_item.add_new((0x0018, 0x9370), 'US', 1)  # XRayDetectorIndex
        detector_item.add_new((0x0018, 0x9371), 'UC', 'DETECTOR_001')  # XRayDetectorID
        detector_item.add_new((0x0018, 0x9372), 'CS', invalid_detector_type)  # Invalid detector type
        
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000"
                    )
                ],
                x_ray_detector_sequence=[detector_item],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    ),
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=2,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        dataset = module.to_dataset()
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        assert len(result.warnings) >= 1
        assert any(
            "Multi-energy Detector Type" in warning
            for warning in result.warnings
        )

    # Validation Configuration Tests

    def test_validation_config_disable_sequences(self):
        """Test that disabling sequence validation works."""
        config = ValidationConfig()
        config.validate_sequences = False
        
        dataset = Dataset()
        dataset.add_new((0x0018, 0x9362), 'SQ', [])  # Empty sequence (should normally fail)
        
        result = MultiEnergyCTImageValidator.validate(dataset, config)
        sequence_errors = [
            error for error in result.errors
            if "must contain at least one Item" in error
        ]
        assert len(sequence_errors) == 0

    def test_validation_config_disable_conditional_requirements(self):
        """Test that disabling conditional requirements validation works."""
        config = ValidationConfig()
        config.validate_conditional_requirements = False
        
        # Create source with SWITCHING_SOURCE but no switching phase number
        source_item = Dataset()
        source_item.add_new((0x0018, 0x9366), 'US', 1)
        source_item.add_new((0x0018, 0x9367), 'UC', 'SOURCE_001')
        source_item.add_new((0x0018, 0x9368), 'CS', 'SWITCHING_SOURCE')
        source_item.add_new((0x0018, 0x9369), 'DT', '20240101120000.000000')
        source_item.add_new((0x0018, 0x936A), 'DT', '20240101120030.000000')
        # Missing switching phase number - but conditional validation is disabled
        
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[source_item],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    ),
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=2,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        dataset = module.to_dataset()
        
        result = MultiEnergyCTImageValidator.validate(dataset, config)
        conditional_errors = [
            error for error in result.errors
            if "Switching Phase Number" in error
        ]
        assert len(conditional_errors) == 0

    def test_validation_config_disable_enumerated_values(self):
        """Test that disabling enumerated values validation works."""
        config = ValidationConfig()
        config.check_enumerated_values = False
        
        # Create source with invalid technique
        source_item = Dataset()
        source_item.add_new((0x0018, 0x9366), 'US', 1)
        source_item.add_new((0x0018, 0x9367), 'UC', 'SOURCE_001')
        source_item.add_new((0x0018, 0x9368), 'CS', 'INVALID_TECHNIQUE')  # Invalid but enum validation disabled
        source_item.add_new((0x0018, 0x9369), 'DT', '20240101120000.000000')
        source_item.add_new((0x0018, 0x936A), 'DT', '20240101120030.000000')
        
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[source_item],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    ),
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=2,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        dataset = module.to_dataset()
        
        result = MultiEnergyCTImageValidator.validate(dataset, config)
        enum_warnings = [
            warning for warning in result.warnings
            if "Multi-energy Source Technique" in warning
        ]
        assert len(enum_warnings) == 0

    # Error Message Quality Tests

    def test_error_messages_include_dicom_tags(self):
        """Test that error messages include DICOM tag references."""
        dataset = Dataset()  # Empty dataset to trigger errors
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        assert len(result.errors) > 0
        
        # Check that errors include DICOM tag references in format (xxxx,xxxx)
        import re
        tag_pattern = r"\(\d{4},\d{4}\)"
        errors_with_tags = [
            error for error in result.errors if re.search(tag_pattern, error)
        ]
        assert len(errors_with_tags) > 0

    def test_error_messages_are_descriptive(self):
        """Test that error messages are human-readable and descriptive."""
        dataset = Dataset()
        dataset.add_new((0x0018, 0x9362), 'SQ', [])  # Empty sequence
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        assert len(result.errors) > 0
        
        # Check that error message explains the requirement
        descriptive_errors = [
            error for error in result.errors
            if "must contain at least one Item" in error
        ]
        assert len(descriptive_errors) > 0

    def test_validation_result_format_consistency(self):
        """Test that ValidationResult format is consistent."""
        dataset = Dataset()  # Empty to trigger errors
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        
        # Ensure ValidationResult has required attributes
        assert hasattr(result, "errors")
        assert hasattr(result, "warnings")
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
        
        # Ensure all errors and warnings are strings
        for error in result.errors:
            assert isinstance(error, str)
            assert len(error) > 0
        
        for warning in result.warnings:
            assert isinstance(warning, str)
            assert len(warning) > 0

    # Complex Scenario Tests

    def test_complex_multi_energy_scenario(self):
        """Test complex multi-energy CT scenario with all conditional logic satisfied."""
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    # SWITCHING_SOURCE with switching phase number
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.SWITCHING_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000",
                        switching_phase_number=1
                    ),
                    # CONSTANT_SOURCE
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=2,
                        x_ray_source_id="SOURCE_002",
                        multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
                        source_start_datetime="20240101120030.000000",
                        source_end_datetime="20240101120060.000000"
                    )
                ],
                x_ray_detector_sequence=[
                    # PHOTON_COUNTING with energy ranges
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.PHOTON_COUNTING,
                        nominal_max_energy=120.0,
                        nominal_min_energy=20.0
                    ),
                    # INTEGRATING detector
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=2,
                        x_ray_detector_id="DETECTOR_002",
                        multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
                    )
                ],
                path_sequence=[
                    # Multiple paths with valid references
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    ),
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=2,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=2
                    ),
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=3,
                        referenced_x_ray_source_index=2,
                        referenced_x_ray_detector_index=1
                    ),
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=4,
                        referenced_x_ray_source_index=2,
                        referenced_x_ray_detector_index=2
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        dataset = module.to_dataset()
        
        result = MultiEnergyCTImageValidator.validate(dataset, self.config)
        assert len(result.errors) == 0, f"Unexpected errors: {result.errors}"
        assert len(result.warnings) == 0, f"Unexpected warnings: {result.warnings}"