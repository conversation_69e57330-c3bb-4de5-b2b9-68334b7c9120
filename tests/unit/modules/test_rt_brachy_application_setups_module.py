"""
Test RTBrachyApplicationSetupsModule functionality.

RTBrachyApplicationSetupsModule implements DICOM PS3.3 C.8.8.15
RT Brachy Application Setups Module using composition-based architecture.
"""

import pytest
from pyrt_dicom.modules import RTBrachyApplicationSetupsModule
from pyrt_dicom.enums.rt_enums import (
    BrachyTreatmentTechnique, BrachyTreatmentType, ApplicationSetupType,
    SourceType, SourceMovementType, BrachyAccessoryDeviceType,
    SourceApplicatorType, SourceStrengthUnits
)
from pyrt_dicom.validators import ValidationResult
from pydicom import Dataset
import pydicom


class TestRTBrachyApplicationSetupsModule:
    """Test RTBrachyApplicationSetupsModule functionality with composition-based architecture."""

    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        # Create sample source sequence item
        source_item = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=1,
            source_type=SourceType.POINT,
            source_isotope_name="Ir-192",
            source_isotope_half_life=73.8,
            reference_air_kerma_rate=40800.0,
            source_strength_reference_date="20231201",
            source_strength_reference_time="120000"
        )

        # Create sample treatment machine sequence item
        machine_item = RTBrachyApplicationSetupsModule.create_treatment_machine_item(
            treatment_machine_name="HDR Unit 1"
        )

        # Create sample application setup sequence item
        setup_item = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1
        )

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[machine_item],
            source_sequence=[source_item],
            application_setup_sequence=[setup_item]
        )

        # Test dataset generation
        dataset = module.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)
        assert dataset.BrachyTreatmentTechnique == BrachyTreatmentTechnique.INTERSTITIAL.value
        assert dataset.BrachyTreatmentType == BrachyTreatmentType.HDR.value
        assert len(dataset.TreatmentMachineSequence) == 1
        assert len(dataset.SourceSequence) == 1
        assert len(dataset.ApplicationSetupSequence) == 1
    
    def test_required_elements_validation(self):
        """Test validation of required elements."""
        # Test minimal creation with required fields
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique="",
            brachy_treatment_type="",
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        # Test dataset generation with empty values
        dataset = module.to_dataset()
        assert dataset.BrachyTreatmentTechnique == ""
        assert dataset.BrachyTreatmentType == ""
        assert len(dataset.TreatmentMachineSequence) == 0
        assert len(dataset.SourceSequence) == 0
        assert len(dataset.ApplicationSetupSequence) == 0
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        ).with_optional_elements()

        # Should return self for method chaining
        assert isinstance(module, RTBrachyApplicationSetupsModule)
    
    def test_create_source_item(self):
        """Test source item creation."""
        source = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=1,
            source_type=SourceType.POINT,
            source_isotope_name="Ir-192",
            source_isotope_half_life=73.8,
            reference_air_kerma_rate=40800.0,
            source_strength_reference_date="20231201",
            source_strength_reference_time="120000"
        )

        assert source.SourceNumber == 1
        assert source.SourceType == SourceType.POINT.value
        assert source.SourceIsotopeName == "Ir-192"
        assert source.SourceIsotopeHalfLife == 73.8
        assert source.ReferenceAirKermaRate == 40800.0
        assert source.SourceStrengthReferenceDate == "20231201"
        assert source.SourceStrengthReferenceTime == "120000"
    
    def test_create_source_item_with_optional_elements(self):
        """Test source item creation with optional elements."""
        source = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=1,
            source_type=SourceType.POINT,
            source_isotope_name="Ir-192",
            source_isotope_half_life=73.8,
            reference_air_kerma_rate=40800.0,
            source_strength_reference_date="20231201",
            source_strength_reference_time="120000",
            source_serial_number="SN123456",
            source_model_id="MODEL_A",
            source_description="HDR Source",
            source_manufacturer="ACME Corp"
        )

        assert source.SourceSerialNumber == "SN123456"
        assert source.SourceModelID == "MODEL_A"
        assert source.SourceDescription == "HDR Source"
        assert source.SourceManufacturer == "ACME Corp"
    
    def test_create_treatment_machine_item(self):
        """Test treatment machine item creation."""
        machine = RTBrachyApplicationSetupsModule.create_treatment_machine_item(
            treatment_machine_name="HDR Unit 1"
        )

        assert machine.TreatmentMachineName == "HDR Unit 1"

    def test_create_treatment_machine_item_with_optional_elements(self):
        """Test treatment machine item creation with optional elements."""
        machine = RTBrachyApplicationSetupsModule.create_treatment_machine_item(
            treatment_machine_name="HDR Unit 1",
            manufacturer="ACME Corp",
            institution_name="General Hospital",
            manufacturers_model_name="HDR-2000"
        )

        assert machine.TreatmentMachineName == "HDR Unit 1"
        assert machine.Manufacturer == "ACME Corp"
        assert machine.InstitutionName == "General Hospital"
        assert machine.ManufacturerModelName == "HDR-2000"
    
    def test_create_application_setup_item(self):
        """Test application setup item creation."""
        setup = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1
        )

        assert setup.ApplicationSetupType == ApplicationSetupType.FLETCHER_SUIT.value
        assert setup.ApplicationSetupNumber == 1
        assert setup.TotalReferenceAirKerma == 0.0  # Default value

    def test_create_application_setup_item_with_optional_elements(self):
        """Test application setup item creation with optional elements."""
        setup = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1,
            application_setup_name="Prostate Implant",
            total_reference_air_kerma=1000.0
        )

        assert setup.ApplicationSetupType == ApplicationSetupType.FLETCHER_SUIT.value
        assert setup.ApplicationSetupNumber == 1
        assert setup.ApplicationSetupName == "Prostate Implant"
        assert setup.TotalReferenceAirKerma == 1000.0
    
    def test_has_application_setups_property(self):
        """Test has_application_setups property."""
        # Test without setup sequence
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        # Empty sequence should return False
        assert not module.has_application_setups

        # Add a setup and test again
        setup_item = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1
        )
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[setup_item]
        )
        assert module.has_application_setups
    
    def test_application_setup_count_property(self):
        """Test application_setup_count property."""
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        # Initially no setups
        assert module.application_setup_count == 0

        # Add some setups
        setup1 = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1
        )
        setup2 = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.MANCHESTER,
            application_setup_number=2
        )
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[setup1, setup2]
        )
        assert module.application_setup_count == 2
    
    def test_get_application_setup_numbers(self):
        """Test get_application_setup_numbers method."""
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        # Initially no setup numbers
        assert module.get_application_setup_numbers() == []

        # Add some setups
        setup1 = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1
        )
        setup2 = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.MANCHESTER,
            application_setup_number=3
        )
        setup3 = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.HENSCHKE,
            application_setup_number=2
        )
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[setup1, setup2, setup3]
        )
        numbers = module.get_application_setup_numbers()
        assert len(numbers) == 3
        assert 1 in numbers
        assert 2 in numbers
        assert 3 in numbers
    
    def test_get_application_setup_by_number(self):
        """Test get_application_setup_by_number method."""
        # Add some setups
        setup1 = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1,
            application_setup_name="Setup 1"
        )
        setup2 = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.MANCHESTER,
            application_setup_number=2,
            application_setup_name="Setup 2"
        )
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[setup1, setup2]
        )

        # Test finding existing setup
        found_setup = module.get_application_setup_by_number(1)
        assert found_setup is not None
        assert found_setup.ApplicationSetupName == 'Setup 1'

        # Test finding non-existent setup
        not_found = module.get_application_setup_by_number(99)
        assert not_found is None
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        assert hasattr(module, 'validate')
        assert callable(module.validate)

        # Test validation result structure
        validation_result = module.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)

    def test_new_properties(self):
        """Test new properties added with composition-based architecture."""
        # Create a module with sources and treatment machine
        source_item = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=1,
            source_type=SourceType.POINT,
            source_isotope_name="Ir-192",
            source_isotope_half_life=73.8,
            reference_air_kerma_rate=40800.0,
            source_strength_reference_date="20231201",
            source_strength_reference_time="120000"
        )

        machine_item = RTBrachyApplicationSetupsModule.create_treatment_machine_item(
            treatment_machine_name="HDR Unit 1"
        )

        setup_item = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1
        )

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[machine_item],
            source_sequence=[source_item],
            application_setup_sequence=[setup_item]
        )

        # Test properties
        assert module.has_sources
        assert module.source_count == 1
        assert module.has_treatment_machine
        assert module.is_configured
        assert not module.is_pdr_treatment
        assert not module.is_permanent_implant

        # Test source-related methods
        assert module.get_source_numbers() == [1]
        assert module.get_source_by_number(1) is not None
        assert module.get_source_by_number(99) is None

    def test_pdr_treatment_detection(self):
        """Test PDR treatment detection."""
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.PDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        assert module.is_pdr_treatment

    def test_permanent_implant_detection(self):
        """Test permanent implant detection."""
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.PERMANENT,
            brachy_treatment_type=BrachyTreatmentType.LDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        assert module.is_permanent_implant