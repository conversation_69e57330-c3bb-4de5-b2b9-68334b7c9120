"""
Image Module Enums - Enumerated values for image-related DICOM modules.

This module contains enumerated values used across various image modules
including General Image, Image Pixel, Multi-frame, Cine, and Overlay Plane modules.
"""
from enum import Enum


class QualityControlImage(Enum):
    """Quality Control Image (0028,0300) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.6.1:
    - YES = Image contains only quality control material
    - NO = Image does not contain quality control material
    - BOTH = Image contains both subject and quality control information
    """
    YES = "YES"
    NO = "NO"
    BOTH = "BOTH"


class BurnedInAnnotation(Enum):
    """Burned In Annotation (0028,0301) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.6.1:
    - YES = Image contains sufficient burned in annotation to identify patient and date
    - NO = Image does not contain burned in annotation
    """
    YES = "YES"
    NO = "NO"


class RecognizableVisualFeatures(Enum):
    """Recognizable Visual Features (0028,0302) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.6.1:
    - YES = Image contains sufficiently recognizable visual features to identify patient
    - NO = Image does not contain recognizable visual features
    """
    YES = "YES"
    NO = "NO"


class LossyImageCompression(Enum):
    """Lossy Image Compression (0028,2110) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.6.1:
    - 00 = Image has not been subjected to lossy compression
    - 01 = Image has been subjected to lossy compression
    """
    NOT_COMPRESSED = "00"
    COMPRESSED = "01"


class PresentationLUTShape(Enum):
    """Presentation LUT Shape (2050,0020) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.6.1:
    - IDENTITY = Output is in P-Values (for MONOCHROME2 or color interpretations)
    - INVERSE = Output after inversion is in P-Values (for MONOCHROME1)
    """
    IDENTITY = "IDENTITY"
    INVERSE = "INVERSE"


class ImageLaterality(Enum):
    """Image Laterality (0020,0062) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.6.1:
    - R = Right
    - L = Left
    - U = Unpaired
    - B = Both left and right
    """
    RIGHT = "R"
    LEFT = "L"
    UNPAIRED = "U"
    BOTH = "B"


class PhotometricInterpretation(Enum):
    """Photometric Interpretation (0028,0004) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.6.3.1.2:
    - MONOCHROME1 = Single monochrome image, minimum sample value displayed as white
    - MONOCHROME2 = Single monochrome image, minimum sample value displayed as black
    - PALETTE_COLOR = Color image with single sample per pixel using palette lookup
    - RGB = Color image described by red, green, and blue image planes
    - YBR_FULL = Color image described by luminance (Y) and chrominance (CB, CR) planes
    - YBR_FULL_422 = YBR with horizontal chrominance subsampling
    - YBR_PARTIAL_420 = YBR with horizontal and vertical chrominance subsampling
    - YBR_ICT = YBR with Irreversible Color Transformation
    - YBR_RCT = YBR with Reversible Color Transformation
    - XYB = XYB color model
    """
    MONOCHROME1 = "MONOCHROME1"
    MONOCHROME2 = "MONOCHROME2"
    PALETTE_COLOR = "PALETTE COLOR"
    RGB = "RGB"
    YBR_FULL = "YBR_FULL"
    YBR_FULL_422 = "YBR_FULL_422"
    YBR_PARTIAL_420 = "YBR_PARTIAL_420"
    YBR_ICT = "YBR_ICT"
    YBR_RCT = "YBR_RCT"
    XYB = "XYB"


class PlanarConfiguration(Enum):
    """Planar Configuration (0028,0006) - DICOM VR: US

    Defined Terms per DICOM PS3.3 C.7.6.3:
    - 0 = Color-by-pixel format (R1,G1,B1,R2,G2,B2,...)
    - 1 = Color-by-plane format (R1,R2,R3,...,G1,G2,G3,...,B1,B2,B3,...)
    """
    COLOR_BY_PIXEL = 0
    COLOR_BY_PLANE = 1


class PixelRepresentation(Enum):
    """Pixel Representation (0028,0103) - DICOM VR: US
    
    Defined Terms per DICOM PS3.3 C.7.6.3:
    - 0 = Unsigned integer
    - 1 = 2's complement (signed integer)
    """
    UNSIGNED = 0
    SIGNED = 1


class StereoPairsPresent(Enum):
    """Stereo Pairs Present (0022,0028) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.6.6:
    - YES = Multi-frame image consists of stereoscopic pairs
    - NO = Multi-frame image does not consist of stereoscopic pairs
    """
    YES = "YES"
    NO = "NO"


class PreferredPlaybackSequencing(Enum):
    """Preferred Playback Sequencing (0018,1244) - DICOM VR: US
    
    Defined Terms per DICOM PS3.3 C.7.6.5:
    - 0 = Looping (1,2,...,n,1,2,...,n,1,2,...,n,...)
    - 1 = Sweeping (1,2,...,n,n-1,...,2,1,2,...,n,...)
    """
    LOOPING = 0
    SWEEPING = 1


class ChannelMode(Enum):
    """Channel Mode (003A,0302) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.6.5:
    - MONO = 1 signal
    - STEREO = 2 simultaneously acquired (left and right) signals
    """
    MONO = "MONO"
    STEREO = "STEREO"


class OverlayType(Enum):
    """Overlay Type (60xx,0040) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.9.2:
    - G = Graphics overlay
    - R = Region of Interest overlay
    """
    GRAPHICS = "G"
    ROI = "R"


class OverlaySubtype(Enum):
    """Overlay Subtype (60xx,0045) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.9.2:
    - USER = User created graphic annotation
    - AUTOMATED = Machine or algorithm generated annotation
    - ACTIVE_IMAGE_AREA = Identification of active area
    """
    USER = "USER"
    AUTOMATED = "AUTOMATED"
    ACTIVE_IMAGE_AREA = "ACTIVE IMAGE AREA"


class LossyImageCompressionMethod(Enum):
    """Lossy Image Compression Method (0028,2114) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.6.1:
    - ISO_10918_1 = JPEG Lossy Compression
    - ISO_14495_1 = JPEG-LS Near-lossless Compression
    - ISO_15444_1 = JPEG 2000 Irreversible Compression
    - ISO_15444_15 = High-Throughput JPEG 2000 Irreversible
    - ISO_18181_1 = JPEG XL Image Coding System
    - ISO_13818_2 = MPEG2 Compression
    - ISO_14496_10 = MPEG-4 AVC/H.264 Compression
    - ISO_23008_2 = HEVC/H.265 Lossy Compression
    """
    ISO_10918_1 = "ISO_10918_1"
    ISO_14495_1 = "ISO_14495_1"
    ISO_15444_1 = "ISO_15444_1"
    ISO_15444_15 = "ISO_15444_15"
    ISO_18181_1 = "ISO_18181_1"
    ISO_13818_2 = "ISO_13818_2"
    ISO_14496_10 = "ISO_14496_10"
    ISO_23008_2 = "ISO_23008_2"


class ImageType(Enum):
    """Image Type (0008,0008) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.6.1.1.2:
    Value 1 - Pixel Data Characteristics:
    - ORIGINAL = Original Image
    - DERIVED = Derived Image
    
    Value 2 - Patient Examination Characteristics:
    - PRIMARY = Primary Image  
    - SECONDARY = Secondary Image
    """
    ORIGINAL = "ORIGINAL"
    DERIVED = "DERIVED"
    PRIMARY = "PRIMARY"
    SECONDARY = "SECONDARY"


class ModalityLutType(Enum):
    """Modality LUT Type (0028,3004) - DICOM VR: LO
    
    Defined Terms per DICOM PS3.3 C.11.1:
    - OD = Optical density (thousands)
    - HU = Hounsfield Units (CT)
    - US = Unspecified
    - MGML = mg/ml
    - Z_EFF = Effective Atomic Number
    - ED = Electron density in 10^23 electrons/ml
    - EDW = Electron density normalized to water
    - HU_MOD = Modified Hounsfield Unit
    - PCT = Percentage (%)
    """
    OD = "OD"
    HU = "HU"
    US = "US"
    MGML = "MGML"
    Z_EFF = "Z_EFF"
    ED = "ED"
    EDW = "EDW"
    HU_MOD = "HU_MOD"
    PCT = "PCT"


class RescaleType(Enum):
    """Rescale Type (0028,1054) - DICOM VR: LO
    
    Defined Terms per DICOM PS3.3 C.7.6.1:
    - OD = Optical density (thousands)
    - HU = Hounsfield Units (CT)
    - US = Unspecified
    - MGML = mg/ml
    - Z_EFF = Effective Atomic Number
    - ED = Electron density in 10^23 electrons/ml
    - EDW = Electron density normalized to water
    - HU_MOD = Modified Hounsfield Unit
    - PCT = Percentage (%)
    """
    OD = "OD"
    HU = "HU"
    US = "US"
    MGML = "MGML"
    Z_EFF = "Z_EFF"
    ED = "ED"
    EDW = "EDW"
    HU_MOD = "HU_MOD"
    PCT = "PCT"


class VoiLutFunction(Enum):
    """VOI LUT Function (0028,1056) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.11.2:
    - LINEAR = Default linear function
    - LINEAR_EXACT = Exact linear function
    - SIGMOID = Sigmoid function
    """
    LINEAR = "LINEAR"
    LINEAR_EXACT = "LINEAR_EXACT"
    SIGMOID = "SIGMOID"
