"""Modality LUT Module Validator - DICOM PS3.3 C.11.1 validation.

This validator ensures complete compliance with DICOM PS3.3 C.11.1 Modality LUT Module
requirements, including mutual exclusivity constraints, conditional requirements,
and semantic validation of all data elements.
"""

from pydicom import Dataset
from pydicom.multival import MultiValue
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.image_enums import ModalityLutType, RescaleType


class ModalityLutValidator(BaseValidator):
    """Validator for Modality LUT Module requirements.

    Validates DICOM PS3.3 C.11.1 Modality LUT Module compliance including:
    - Mutual exclusivity between Modality LUT Sequence and Rescale parameters
    - Conditional requirements for Type 1C elements
    - Sequence structure and content validation
    - Enumerated value validation
    - Semantic constraints and cross-field dependencies
    """

    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Modality LUT Module requirements on any pydicom Dataset.

        Performs comprehensive validation of DICOM PS3.3 C.11.1 requirements:
        1. Validates mutual exclusivity constraint (either LUT sequence OR rescale parameters)
        2. Validates conditional requirements for Type 1C elements
        3. Validates sequence structure and required sub-attributes
        4. Validates enumerated values against DICOM defined terms
        5. Validates semantic constraints and data consistency

        Args:
            dataset: pydicom Dataset to validate against DICOM PS3.3 C.11.1 requirements
            config: Optional validation configuration to control validation behavior

        Returns:
            ValidationResult containing structured errors and warnings with specific
            DICOM tag references and actionable guidance for resolution
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()

        # Core requirement: Validate mutual exclusivity (DICOM PS3.3 C.11.1)
        ModalityLutValidator._validate_mutual_exclusivity(dataset, result, config)

        # Validate Modality LUT Sequence if present (Type 1C)
        if hasattr(dataset, 'ModalityLUTSequence'):
            ModalityLutValidator._validate_modality_lut_sequence(dataset, result, config)

        # Validate Rescale parameters if present (Type 1C)
        if hasattr(dataset, 'RescaleIntercept'):
            ModalityLutValidator._validate_rescale_parameters(dataset, result, config)

        return result
    
    @staticmethod
    def _validate_mutual_exclusivity(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate mutual exclusivity requirement per DICOM PS3.3 C.11.1.

        Either a Modality LUT Sequence containing a single Item or Rescale Slope and
        Intercept values shall be present but not both.
        """
        has_lut_sequence = hasattr(dataset, 'ModalityLUTSequence')
        has_rescale = hasattr(dataset, 'RescaleIntercept')

        if has_lut_sequence and has_rescale:
            result.add_error(
                "DICOM PS3.3 C.11.1 violation: Modality LUT Sequence (0028,3000) and "
                "Rescale Intercept (0028,1052) shall not both be present. "
                "Either a Modality LUT Sequence OR Rescale parameters must be used, but not both. "
                "Remove one of these conflicting transformation methods."
            )
        elif not has_lut_sequence and not has_rescale:
            result.add_error(
                "DICOM PS3.3 C.11.1 violation: Either Modality LUT Sequence (0028,3000) "
                "or Rescale Intercept (0028,1052) shall be present. "
                "The Modality LUT Module requires exactly one transformation method. "
                "Add either a Modality LUT Sequence with a single item OR complete "
                "Rescale parameters (Intercept, Slope, and Type)."
            )
    
    @staticmethod
    def _validate_modality_lut_sequence(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate Modality LUT Sequence requirements per DICOM PS3.3 C.11.1."""
        if not hasattr(dataset, 'ModalityLUTSequence'):
            return

        sequence = dataset.ModalityLUTSequence

        # DICOM PS3.3 C.11.1: Must contain exactly one item
        if len(sequence) != 1:
            result.add_error(
                f"DICOM PS3.3 C.11.1 violation: Modality LUT Sequence (0028,3000) must contain "
                f"exactly one item, found {len(sequence)} items. "
                "Only a single Item shall be included in this Sequence to ensure "
                "unambiguous definition of the grayscale pipeline input."
            )
            return

        item = sequence[0]

        # Validate required Type 1 elements in sequence item
        required_fields = {
            'LUTDescriptor': '(0028,3002)',
            'ModalityLUTType': '(0028,3004)',
            'LUTData': '(0028,3006)'
        }

        for field, tag in required_fields.items():
            if not hasattr(item, field):
                result.add_error(
                    f"DICOM PS3.3 C.11.1 violation: Missing required Type 1 element "
                    f"{field} {tag} in Modality LUT Sequence item. "
                    f"This element is mandatory for proper LUT transformation."
                )

        # Validate LUT Descriptor format (0028,3002)
        if hasattr(item, 'LUTDescriptor'):
            ModalityLutValidator._validate_lut_descriptor(item.LUTDescriptor, result)

        # Validate Modality LUT Type enumerated values (0028,3004)
        if config.check_enumerated_values and hasattr(item, 'ModalityLUTType'):
            ModalityLutValidator._validate_modality_lut_type(item.ModalityLUTType, result)

        # Validate LUT Data presence and basic structure (0028,3006)
        if hasattr(item, 'LUTData'):
            ModalityLutValidator._validate_lut_data(item, result)
    
    @staticmethod
    def _validate_rescale_parameters(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate Rescale parameters requirements per DICOM PS3.3 C.11.1."""
        # DICOM PS3.3 C.11.1: All three rescale parameters must be present together
        rescale_fields = {
            'RescaleIntercept': '(0028,1052)',
            'RescaleSlope': '(0028,1053)',
            'RescaleType': '(0028,1054)'
        }

        missing_fields = [(field, tag) for field, tag in rescale_fields.items()
                         if not hasattr(dataset, field)]

        if missing_fields:
            missing_list = [f"{field} {tag}" for field, tag in missing_fields]
            result.add_error(
                f"DICOM PS3.3 C.11.1 violation: Missing required rescale fields: {', '.join(missing_list)}. "
                "All rescale parameters (Intercept, Slope, Type) are Type 1C and must be present together "
                "when Modality LUT Sequence is not present. The relationship is: Output units = m*SV + b, "
                "where m=RescaleSlope, b=RescaleIntercept, SV=stored values."
            )

        # Validate Rescale Type enumerated values (0028,1054)
        if config.check_enumerated_values and hasattr(dataset, 'RescaleType'):
            ModalityLutValidator._validate_rescale_type(dataset.RescaleType, result)

        # Validate numeric values with semantic constraints
        if hasattr(dataset, 'RescaleSlope'):
            ModalityLutValidator._validate_rescale_slope(dataset.RescaleSlope, result)

        if hasattr(dataset, 'RescaleIntercept'):
            ModalityLutValidator._validate_rescale_intercept(dataset.RescaleIntercept, result)

    @staticmethod
    def _validate_lut_descriptor(lut_descriptor, result: ValidationResult) -> None:
        """Validate LUT Descriptor format per DICOM PS3.3 C.********."""
        if not isinstance(lut_descriptor, (list, tuple, MultiValue)) or len(lut_descriptor) != 3:
            result.add_error(
                "DICOM PS3.3 C.******** violation: LUT Descriptor (0028,3002) must contain "
                "exactly 3 values [entries, first_value, bits_per_entry]. "
                f"Found {len(lut_descriptor) if hasattr(lut_descriptor, '__len__') else 'invalid'} values. "
                "The three values describe: (1) number of entries in lookup table, "
                "(2) first stored pixel value mapped, (3) number of bits for each entry."
            )
            return

        # Validate third value (bits per entry) per DICOM PS3.3 C.********
        bits_per_entry = lut_descriptor[2]
        if bits_per_entry not in [8, 16]:
            result.add_error(
                f"DICOM PS3.3 C.******** violation: LUT Descriptor (0028,3002) third value "
                f"(bits per entry) must be 8 or 16, found {bits_per_entry}. "
                "This value specifies the number of bits for each entry in the LUT Data "
                "and determines the LUT entry value range (256 for 8-bit, 65536 for 16-bit)."
            )

        # Validate first value (number of entries)
        num_entries = lut_descriptor[0]
        if num_entries == 0:
            # Special case: 0 means 2^16 = 65536 entries
            if bits_per_entry != 16:
                result.add_warning(
                    "LUT Descriptor (0028,3002) first value is 0 (meaning 65536 entries) "
                    f"but bits per entry is {bits_per_entry}. Consider using 16 bits per entry "
                    "for optimal compatibility with 65536 entries."
                )
        elif num_entries < 1:
            result.add_error(
                f"DICOM PS3.3 C.******** violation: LUT Descriptor (0028,3002) first value "
                f"(number of entries) must be positive, found {num_entries}. "
                "Use 0 to represent 65536 entries or a positive integer for the actual count."
            )

    @staticmethod
    def _validate_modality_lut_type(modality_lut_type, result: ValidationResult) -> None:
        """Validate Modality LUT Type enumerated values per DICOM PS3.3 C.********."""
        allowed_values = [e.value for e in ModalityLutType]
        if str(modality_lut_type) not in allowed_values:
            result.add_warning(
                f"DICOM PS3.3 C.********: Modality LUT Type (0028,3004) value '{modality_lut_type}' "
                f"is not a DICOM defined term. Standard values are: {', '.join(allowed_values)}. "
                "Other values are permitted but not defined by the DICOM Standard. "
                "Consider using a defined term for better interoperability."
            )

    @staticmethod
    def _validate_rescale_type(rescale_type, result: ValidationResult) -> None:
        """Validate Rescale Type enumerated values per DICOM PS3.3 C.********."""
        allowed_values = [e.value for e in RescaleType]
        if str(rescale_type) not in allowed_values:
            result.add_warning(
                f"DICOM PS3.3 C.********: Rescale Type (0028,1054) value '{rescale_type}' "
                f"is not a DICOM defined term. Standard values are: {', '.join(allowed_values)}. "
                "Other values are permitted but not defined by the DICOM Standard. "
                "Consider using a defined term for better interoperability."
            )

    @staticmethod
    def _validate_rescale_slope(rescale_slope, result: ValidationResult) -> None:
        """Validate Rescale Slope numeric value per DICOM PS3.3 C.11.1."""
        try:
            slope = float(rescale_slope)
            if slope == 0:
                result.add_warning(
                    "Rescale Slope (0028,1053) value is 0, which may cause division by zero "
                    "in inverse calculations. Verify this is the intended transformation. "
                    "The relationship is: Output units = m*SV + b, where m=RescaleSlope."
                )
        except (ValueError, TypeError):
            result.add_error(
                f"DICOM PS3.3 C.11.1 violation: Rescale Slope (0028,1053) must be a valid "
                f"numeric value, found '{rescale_slope}' of type {type(rescale_slope).__name__}. "
                "This value represents the 'm' coefficient in the linear transformation: "
                "Output units = m*SV + b."
            )

    @staticmethod
    def _validate_rescale_intercept(rescale_intercept, result: ValidationResult) -> None:
        """Validate Rescale Intercept numeric value per DICOM PS3.3 C.11.1."""
        try:
            float(rescale_intercept)
        except (ValueError, TypeError):
            result.add_error(
                f"DICOM PS3.3 C.11.1 violation: Rescale Intercept (0028,1052) must be a valid "
                f"numeric value, found '{rescale_intercept}' of type {type(rescale_intercept).__name__}. "
                "This value represents the 'b' coefficient in the linear transformation: "
                "Output units = m*SV + b."
            )

    @staticmethod
    def _validate_lut_data(item, result: ValidationResult) -> None:
        """Validate LUT Data basic structure per DICOM PS3.3 C.11.1."""
        lut_data = item.LUTData

        if not lut_data:
            result.add_error(
                "DICOM PS3.3 C.11.1 violation: LUT Data (0028,3006) cannot be empty. "
                "This element contains the actual lookup table values for the transformation."
            )
            return

        # Cross-validate with LUT Descriptor if present
        if hasattr(item, 'LUTDescriptor') and len(item.LUTDescriptor) >= 1:
            expected_entries = item.LUTDescriptor[0]
            if expected_entries == 0:
                expected_entries = 65536  # Special case per DICOM standard

            actual_entries = len(lut_data)
            if actual_entries != expected_entries:
                result.add_warning(
                    f"LUT Data (0028,3006) contains {actual_entries} entries but "
                    f"LUT Descriptor (0028,3002) specifies {item.LUTDescriptor[0]} entries "
                    f"({'65536' if item.LUTDescriptor[0] == 0 else item.LUTDescriptor[0]} expected). "
                    "Verify the LUT data matches the descriptor specification."
                )
