"""ROI Contour Module DICOM validation - PS3.3 C.8.8.6"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.rt_enums import ContourGeometricType


class ROIContourValidator(BaseValidator):
    """Validator for DICOM ROI Contour Module (PS3.3 C.8.8.6).
    
    Validates ROI contour data structure and conditional requirements according
    to DICOM PS3.3 Section C.8.8.6. Ensures proper sequence structure, geometric
    type constraints, and XOR contour consistency rules.
    
    Key Validations:
    - ROI Contour Sequence structure and required elements
    - Contour geometric type enumerated values and point count constraints
    - XOR contour consistency (all contours in ROI must match if any are XOR)
    - Contour data length consistency (3 × number of points)
    - Source pixel planes and series sequence requirements when present
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate ROI Contour Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate enumerated values
        if config.check_enumerated_values:
            ROIContourValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            ROIContourValidator._validate_sequence_requirements(dataset, result)
        
        # Validate contour data consistency
        ROIContourValidator._validate_contour_data_consistency(dataset, result)
        
        # Validate source pixel planes usage
        ROIContourValidator._validate_source_pixel_planes_usage(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications."""
        
        # Validate Contour Geometric Type in Contour Sequence
        roi_contour_seq = getattr(dataset, 'ROIContourSequence', [])
        for i, roi_item in enumerate(roi_contour_seq):
            contour_seq = roi_item.get('ContourSequence', [])
            for j, contour_item in enumerate(contour_seq):
                geometric_type = contour_item.get('ContourGeometricType', '')
                if geometric_type:
                    valid_types = [gtype.value for gtype in ContourGeometricType]
                    BaseValidator.validate_enumerated_value(
                        geometric_type, valid_types,
                        f"Contour Geometric Type (3006,0042) in ROI {i}, Contour {j}", result
                    )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        # ROI Contour Sequence validation (Type 1)
        roi_contour_seq = getattr(dataset, 'ROIContourSequence', [])
        if not roi_contour_seq:
            result.add_error(
                "ROI Contour Sequence (3006,0039) is required (Type 1)"
            )
        
        for i, roi_item in enumerate(roi_contour_seq):
            # Referenced ROI Number is Type 1
            if not roi_item.get('ReferencedROINumber'):
                result.add_error(
                    f"ROI Contour Sequence item {i}: "
                    "Referenced ROI Number (3006,0084) is required"
                )
            
            # ROI Display Color is Type 1
            roi_display_color = roi_item.get('ROIDisplayColor')
            if not roi_display_color:
                result.add_error(
                    f"ROI Contour Sequence item {i}: "
                    "ROI Display Color (3006,002A) is required"
                )
            elif len(roi_display_color) != 3:
                result.add_error(
                    f"ROI Contour Sequence item {i}: "
                    "ROI Display Color (3006,002A) must be RGB triplet (3 values). "
                    "Provide exactly 3 integer values for red, green, and blue components."
                )
            elif any(not (0 <= color <= 255) for color in roi_display_color):
                result.add_warning(
                    f"ROI Contour Sequence item {i}: "
                    "ROI Display Color values should be in range 0-255"
                )
            
            # Validate Contour Sequence if present
            contour_seq = roi_item.get('ContourSequence', [])
            for j, contour_item in enumerate(contour_seq):
                # Contour Geometric Type is Type 1
                if not contour_item.get('ContourGeometricType'):
                    result.add_error(
                        f"Contour Sequence item {j} in ROI {i}: "
                        "Contour Geometric Type (3006,0042) is required"
                    )
                
                # Number of Contour Points is Type 1
                if not contour_item.get('NumberOfContourPoints'):
                    result.add_error(
                        f"Contour Sequence item {j} in ROI {i}: "
                        "Number of Contour Points (3006,0046) is required"
                    )
                
                # Contour Data is Type 1
                if not contour_item.get('ContourData'):
                    result.add_error(
                        f"Contour Sequence item {j} in ROI {i}: "
                        "Contour Data (3006,0050) is required"
                    )
                
                # Validate Contour Image Sequence if present
                contour_image_seq = contour_item.get('ContourImageSequence', [])
                for k, image_item in enumerate(contour_image_seq):
                    if not image_item.get('ReferencedSOPClassUID'):
                        result.add_error(
                            f"Contour Image Sequence item {k} in Contour {j}, ROI {i}: "
                            "Referenced SOP Class UID (0008,1150) is required"
                        )
                    if not image_item.get('ReferencedSOPInstanceUID'):
                        result.add_error(
                            f"Contour Image Sequence item {k} in Contour {j}, ROI {i}: "
                            "Referenced SOP Instance UID (0008,1155) is required"
                        )
            
            # Validate Source Pixel Planes Characteristics Sequence if present
            source_pixel_seq = roi_item.get('SourcePixelPlanesCharacteristicsSequence', [])
            for j, pixel_item in enumerate(source_pixel_seq):
                required_pixel_fields = [
                    ('PixelSpacing', '0028,0030'),
                    ('SpacingBetweenSlices', '0018,0088'),
                    ('ImageOrientationPatient', '0020,0037'),
                    ('ImagePositionPatient', '0020,0032'),
                    ('NumberOfFrames', '0028,0008'),
                    ('Rows', '0028,0010'),
                    ('Columns', '0028,0011')
                ]
                
                for field_name, tag in required_pixel_fields:
                    if not pixel_item.get(field_name):
                        result.add_error(
                            f"Source Pixel Planes Characteristics Sequence item {j} in ROI {i}: "
                            f"{field_name} ({tag}) is required"
                        )
            
            # Validate Source Series Sequence if present
            source_series_seq = roi_item.get('SourceSeriesSequence', [])
            for j, series_item in enumerate(source_series_seq):
                if not series_item.get('SeriesInstanceUID'):
                    result.add_error(
                        f"Source Series Sequence item {j} in ROI {i}: "
                        "Series Instance UID (0020,000E) is required"
                    )
    
    @staticmethod
    def _validate_contour_data_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate contour data consistency and logical constraints."""
        
        roi_contour_seq = getattr(dataset, 'ROIContourSequence', [])
        for i, roi_item in enumerate(roi_contour_seq):
            contour_seq = roi_item.get('ContourSequence', [])
            
            # Check for CLOSEDPLANAR_XOR consistency
            has_xor_contours = False
            has_non_xor_contours = False
            
            for j, contour_item in enumerate(contour_seq):
                geometric_type = contour_item.get('ContourGeometricType', '')
                number_of_points = contour_item.get('NumberOfContourPoints', 0)
                contour_data = contour_item.get('ContourData', [])
                
                # Track XOR vs non-XOR contours
                if geometric_type == 'CLOSEDPLANAR_XOR':
                    has_xor_contours = True
                elif geometric_type in ['CLOSED_PLANAR', 'OPEN_PLANAR', 'OPEN_NONPLANAR', 'POINT']:
                    has_non_xor_contours = True
                
                # Validate contour data length consistency
                if number_of_points and contour_data:
                    expected_data_length = number_of_points * 3  # Each point has x, y, z
                    if len(contour_data) != expected_data_length:
                        result.add_error(
                            f"Contour Sequence item {j} in ROI {i}: "
                            f"Contour Data length ({len(contour_data)}) should be 3 times "
                            f"Number of Contour Points ({number_of_points})"
                        )

                # Validate contour offset vector length
                contour_offset_vector = contour_item.get('ContourOffsetVector', [])
                if contour_offset_vector and len(contour_offset_vector) != 3:
                    result.add_error(
                        f"Contour Sequence item {j} in ROI {i}: "
                        f"Contour Offset Vector (3006,0045) must contain exactly 3 values (x,y,z)"
                    )

                # Validate contour slab thickness is positive
                contour_slab_thickness = contour_item.get('ContourSlabThickness')
                if contour_slab_thickness is not None and contour_slab_thickness <= 0:
                    result.add_warning(
                        f"Contour Sequence item {j} in ROI {i}: "
                        f"Contour Slab Thickness ({contour_slab_thickness}) should be positive"
                    )
                
                # Validate geometric type constraints
                if geometric_type == 'POINT' and number_of_points != 1:
                    result.add_error(
                        f"Contour Sequence item {j} in ROI {i}: "
                        "POINT geometric type must have exactly 1 contour point"
                    )
                elif geometric_type in ['OPEN_PLANAR', 'OPEN_NONPLANAR'] and number_of_points < 2:
                    result.add_warning(
                        f"Contour Sequence item {j} in ROI {i}: "
                        "Open contours should have at least 2 points"
                    )
                elif geometric_type in ['CLOSED_PLANAR', 'CLOSEDPLANAR_XOR'] and number_of_points < 3:
                    result.add_warning(
                        f"Contour Sequence item {j} in ROI {i}: "
                        "Closed contours should have at least 3 points"
                    )

                # Validate closed contour closure (first and last points should be the same)
                if geometric_type in ['CLOSED_PLANAR', 'CLOSEDPLANAR_XOR'] and contour_data and len(contour_data) >= 6:
                    first_point = contour_data[:3]
                    last_point = contour_data[-3:]
                    if first_point != last_point:
                        result.add_warning(
                            f"Contour Sequence item {j} in ROI {i}: "
                            "Closed contour should have first and last points identical"
                        )
            
            # Validate XOR contour consistency rule (DICOM PS3.3 C.8.8.6.3)
            if has_xor_contours and has_non_xor_contours:
                result.add_error(
                    f"ROI Contour Sequence item {i}: "
                    "DICOM PS3.3 C.8.8.6.3 requires that if any contour in an ROI has "
                    "Contour Geometric Type (3006,0042) CLOSEDPLANAR_XOR, all contours "
                    "in that ROI must be CLOSEDPLANAR_XOR. Mixed geometric types within "
                    "an ROI are not permitted when XOR contours are present."
                )
    
    @staticmethod
    def _validate_source_pixel_planes_usage(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Source Pixel Planes Characteristics usage per DICOM PS3.3 C.8.8.6.4."""
        
        roi_contour_seq = getattr(dataset, 'ROIContourSequence', [])
        for i, roi_item in enumerate(roi_contour_seq):
            source_pixel_seq = roi_item.get('SourcePixelPlanesCharacteristicsSequence', [])
            if source_pixel_seq:
                # Check if any contours have geometric types that make pixel planes not useful
                contour_seq = roi_item.get('ContourSequence', [])
                non_useful_types = ['POINT', 'OPEN_PLANAR', 'OPEN_NONPLANAR']
                
                for j, contour_item in enumerate(contour_seq):
                    geometric_type = contour_item.get('ContourGeometricType', '')
                    if geometric_type in non_useful_types:
                        result.add_warning(
                            f"ROI Contour Sequence item {i}: "
                            f"Source Pixel Planes Characteristics Sequence (3006,004A) is "
                            f"present but may not be useful for Contour Geometric Type "
                            f"'{geometric_type}' (3006,0042). Per DICOM PS3.3 C.8.8.6.4, "
                            f"Source Pixel Planes are not useful for geometric types "
                            f"POINT, OPEN_PLANAR, or OPEN_NONPLANAR."
                        )
                        break  # Only warn once per ROI
