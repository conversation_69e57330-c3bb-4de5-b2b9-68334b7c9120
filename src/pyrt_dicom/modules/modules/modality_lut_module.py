"""
Modality LUT Module - DICOM PS3.3 C.11.1

The Modality LUT Module describes the Modality LUT transformation that converts
manufacturer dependent pixel values to pixel values which are meaningful for
the application.
"""
from pydicom import Dataset
from .base_module import BaseModule
from ...enums.image_enums import ModalityLutType, RescaleType
from ...validators.modules.modality_lut_validator import ModalityLutValidator
from ...validators.modules.base_validator import ValidationConfig
from ...validators import ValidationResult
from ...utils.dicom_formatters import format_enum_value


class ModalityLutModule(BaseModule):
    """Modality LUT Module implementation for DICOM PS3.3 C.11.1.

    Uses composition with internal dataset management for cleaner separation of concerns.
    Describes the Modality LUT transformation that converts manufacturer dependent
    pixel values to pixel values which are meaningful for the application.

    Either a Modality LUT Sequence containing a single Item or Rescale Slope and
    Intercept values shall be present but not both.

    Usage:
        # Create with Modality LUT Sequence (from_required_elements is not needed - no required elements)
        modality_lut = ModalityLutModule.from_required_elements().with_modality_lut_sequence(
            modality_lut_sequence=[
                ModalityLutModule.create_modality_lut_item(
                    lut_descriptor=[256, 0, 8],
                    modality_lut_type=ModalityLutType.HU,
                    lut_data=[0, 1, 2, 3]  # Example LUT data
                )
            ]
        )

        # Or create with Rescale parameters
        modality_lut = ModalityLutModule.from_required_elements().with_rescale_parameters(
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            rescale_type=RescaleType.HU
        )

        # Generate dataset for IOD integration
        dataset = modality_lut.to_dataset()

        # Validate
        result = modality_lut.validate()
    """
    
    @classmethod
    def from_required_elements(cls) -> 'ModalityLutModule':
        """Create module with required elements.
        
        Note: This module has no Type 1 or Type 2 elements - all elements are conditional.
        Either Modality LUT Sequence or Rescale parameters must be provided via conditional methods.
        
        Returns:
            ModalityLutModule: New module instance
        """
        return cls()
    
    def with_optional_elements(self, **kwargs) -> 'ModalityLutModule':
        """Add optional (Type 3) data elements to the module instance.
        
        The Modality LUT Module has no Type 3 elements defined in DICOM PS3.3 C.11.1.
        This method is provided for API consistency but accepts no parameters.
        
        Args:
            **kwargs: No optional elements are supported
            
        Returns:
            ModalityLutModule: Self for method chaining
            
        Raises:
            ValueError: If any keyword arguments are provided
        """
        if kwargs:
            raise ValueError(f"ModalityLutModule has no optional elements. Unexpected arguments: {list(kwargs.keys())}")
        return self
    
    def with_modality_lut_sequence(
        self,
        modality_lut_sequence: list[Dataset]
    ) -> 'ModalityLutModule':
        """Add Modality LUT Sequence (Type 1C).

        Required if Rescale Intercept is not present. Only a single Item shall be included.
        Shall not be present if Rescale Intercept (0028,1052) is present.

        Args:
            modality_lut_sequence: List containing single modality LUT item

        Returns:
            ModalityLutModule: Self for method chaining

        Raises:
            ValueError: If sequence doesn't contain exactly one item or if rescale parameters are already present
        """
        if len(modality_lut_sequence) != 1:
            raise ValueError("Modality LUT Sequence must contain exactly one item")

        # Check for mutual exclusivity with rescale parameters
        if hasattr(self._dataset, 'RescaleIntercept'):
            raise ValueError(
                "Modality LUT Sequence shall not be present if Rescale Intercept is present. "
                "Either Modality LUT Sequence OR Rescale parameters must be used, but not both."
            )

        self._dataset.ModalityLUTSequence = modality_lut_sequence
        return self
    
    def with_rescale_parameters(
        self,
        rescale_intercept: float,
        rescale_slope: float,
        rescale_type: RescaleType | str
    ) -> 'ModalityLutModule':
        """Add Rescale parameters (Type 1C).

        Required if Modality LUT Sequence is not present. All three parameters
        are required together. Shall not be present if Modality LUT Sequence (0028,3000) is present.

        Args:
            rescale_intercept: The value b in relationship between stored values (SV) and output units.
                             Output units = m*SV + b (0028,1052)
            rescale_slope: The value m in the equation specified by Rescale Intercept (0028,1053)
            rescale_type: Specifies the output units of Rescale Slope and Intercept (0028,1054)

        Returns:
            ModalityLutModule: Self for method chaining

        Raises:
            ValueError: If Modality LUT Sequence is already present
        """
        # Check for mutual exclusivity with Modality LUT Sequence
        if hasattr(self._dataset, 'ModalityLUTSequence'):
            raise ValueError(
                "Rescale parameters shall not be present if Modality LUT Sequence is present. "
                "Either Modality LUT Sequence OR Rescale parameters must be used, but not both."
            )

        self._dataset.RescaleIntercept = str(rescale_intercept)
        self._dataset.RescaleSlope = str(rescale_slope)
        self._dataset.RescaleType = format_enum_value(rescale_type)
        return self
    
    @staticmethod
    def create_modality_lut_item(
        lut_descriptor: list[int],
        modality_lut_type: ModalityLutType | str,
        lut_data: list[int],
        lut_explanation: str | None = None
    ) -> Dataset:
        """Create a Modality LUT Sequence item.

        Args:
            lut_descriptor: Format of the LUT Data [entries, first_value, bits_per_entry] (0028,3002).
                          First value: number of entries (0 means 2^16 = 65536)
                          Second value: first stored pixel value mapped
                          Third value: bits per entry (must be 8 or 16)
            modality_lut_type: Specifies the output values of this Modality LUT (0028,3004)
            lut_data: LUT Data values (0028,3006)
            lut_explanation: Optional free form text explanation of the LUT meaning (0028,3003)

        Returns:
            Dataset representing a Modality LUT Sequence item

        Raises:
            ValueError: If lut_descriptor doesn't have exactly 3 values or bits per entry is invalid
        """
        if len(lut_descriptor) != 3:
            raise ValueError("LUT Descriptor must contain exactly 3 values [entries, first_value, bits_per_entry]")

        if lut_descriptor[2] not in [8, 16]:
            raise ValueError("LUT Descriptor third value (bits per entry) must be 8 or 16")

        item = Dataset()
        item.LUTDescriptor = lut_descriptor
        item.ModalityLUTType = format_enum_value(modality_lut_type)
        item.LUTData = lut_data

        if lut_explanation is not None:
            item.LUTExplanation = lut_explanation

        return item
    
    @property
    def has_modality_lut_sequence(self) -> bool:
        """Check if Modality LUT Sequence is present.

        Returns:
            bool: True if Modality LUT Sequence is present
        """
        return hasattr(self._dataset, 'ModalityLUTSequence')

    @property
    def has_rescale_parameters(self) -> bool:
        """Check if Rescale parameters are present.

        Returns:
            bool: True if all rescale parameters are present
        """
        return (hasattr(self._dataset, 'RescaleIntercept') and
                hasattr(self._dataset, 'RescaleSlope') and
                hasattr(self._dataset, 'RescaleType'))

    @property
    def is_configured(self) -> bool:
        """Check if module is properly configured.

        Returns:
            bool: True if either LUT sequence or rescale parameters are present
        """
        return self.has_modality_lut_sequence or self.has_rescale_parameters

    @property
    def uses_lut_sequence(self) -> bool:
        """Check if module uses Modality LUT Sequence transformation.

        Returns:
            bool: True if configured with Modality LUT Sequence
        """
        return self.has_modality_lut_sequence

    @property
    def uses_rescale_transformation(self) -> bool:
        """Check if module uses rescale transformation.

        Returns:
            bool: True if configured with rescale parameters
        """
        return self.has_rescale_parameters

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this Modality LUT Module instance.

        Args:
            config: Optional validation configuration

        Returns:
            ValidationResult with errors and warnings
        """
        return ModalityLutValidator.validate(self._dataset, config)
