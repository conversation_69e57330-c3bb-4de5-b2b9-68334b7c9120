"""
Multi-frame Module Implementation - DICOM PS3.3 C.7.6.6

This module implements the DICOM Multi-frame Module as specified in PS3.3 Section C.7.6.6.
The Multi-frame Module enables storage and management of sequential image data where multiple
frames are combined into a single DICOM object, essential for dynamic imaging modalities,
time series analysis, and volumetric reconstruction workflows.

Multi-frame images are fundamental to many medical imaging applications:
- Dynamic studies: Cardiac cine, perfusion imaging, and functional analysis
- Time series: Temporal bone studies, contrast enhancement tracking, and treatment monitoring
- Volumetric data: CT angiography, MR imaging sequences, and reconstruction datasets
- Stereoscopic imaging: 3D visualization and depth perception applications
- Functional imaging: fMRI, cardiac wall motion, and respiratory-gated acquisitions

The module provides comprehensive frame management including temporal sequencing, stereoscopic
pair coordination, and functional group organization. It supports multiple frame increment
strategies for different imaging scenarios and temporal sampling requirements.

Key features include:
- Frame sequencing control through Frame Increment Pointer management
- Stereoscopic pair detection and validation for 3D imaging workflows
- Temporal frame timing through Frame Time and Frame Time Vector support
- Functional group integration for enhanced multi-frame organization
- Comprehensive validation for clinical compatibility and DICOM compliance
"""
from pydicom.tag import Tag, BaseTag
from .base_module import BaseModule
from ...enums.image_enums import StereoPairsPresent
from ...validators.modules.multi_frame_validator import MultiFrameValidator
from ...validators.modules.base_validator import ValidationConfig
from ...validators import ValidationResult
from ...utils.dicom_formatters import format_enum_value


class MultiFrameModule(BaseModule):
    """DICOM Multi-frame Module implementation conforming to PS3.3 Section C.7.6.6.
    
    This class provides a comprehensive interface for creating and managing multi-frame DICOM
    images where multiple image frames are combined into a single DICOM object. Multi-frame
    imaging is essential for dynamic studies, temporal analysis, volumetric reconstruction,
    and stereoscopic visualization in modern medical imaging workflows.
    
    Multi-frame images enable advanced clinical applications:
    - Cardiac imaging: Cine loops showing heart wall motion and valve function
    - Perfusion studies: Temporal contrast enhancement tracking for tissue characterization
    - Dynamic imaging: Real-time fluoroscopy, angiography, and interventional guidance
    - Volumetric reconstruction: CT/MR volume datasets for 3D visualization and analysis
    - Stereoscopic imaging: Left/right pairs for depth perception and surgical planning
    - Functional studies: fMRI time series, cardiac strain analysis, and motion tracking
    
    Technical Implementation Features:
    - Frame Increment Pointer management for flexible temporal sequencing strategies
    - Support for Frame Time, Frame Time Vector, and Functional Groups sequencing
    - Stereoscopic pair validation and expected frame count calculation
    - Automatic tag format conversion (string, Tag objects, integers) for user convenience
    - Comprehensive frame timing and sequence validation for clinical compatibility
    
    DICOM Compliance:
    This implementation follows DICOM PS3.3 C.7.6.6 specifications exactly, ensuring
    compatibility with all DICOM viewers, PACS systems, and advanced imaging workstations.
    
    Args:
        None (use factory methods for creation)
    
    Example:
        Cardiac cine loop with frame timing:
        
        >>> # Create cardiac cine with 20 frames using frame time
        >>> cardiac_cine = MultiFrameModule.from_required_elements(
        ...     number_of_frames=20,
        ...     frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()
        ... )
        
        >>> # Configure for cardiac imaging workflow
        >>> cardiac_cine.with_optional_elements(
        ...     stereo_pairs_present=StereoPairsPresent.NO,
        ...     encapsulated_pixel_data_value_total_length=2048000
        ... )
        
        Dynamic perfusion study with time vector:
        
        >>> # Create perfusion study with variable frame timing
        >>> perfusion_study = MultiFrameModule.from_required_elements(
        ...     number_of_frames=50,
        ...     frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time_vector()
        ... )
        
        Stereoscopic 3D imaging:
        
        >>> # Create stereoscopic dataset with paired frames
        >>> stereo_volume = MultiFrameModule.from_required_elements(
        ...     number_of_frames=100,  # 50 stereo pairs
        ...     frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_functional_groups()
        ... )
        
        >>> # Enable stereoscopic pair processing
        >>> stereo_volume.with_optional_elements(
        ...     stereo_pairs_present=StereoPairsPresent.YES
        ... )
        
        >>> # Validate stereoscopic configuration
        >>> print(f"Expected stereo pairs: {stereo_volume.expected_stereo_frame_count}")
        Expected stereo pairs: 50
        
        Flexible tag format support:
        
        >>> # Multiple ways to specify frame increment pointer
        >>> # Method 1: Using helper methods (recommended)
        >>> frames1 = MultiFrameModule.from_required_elements(
        ...     number_of_frames=10,
        ...     frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()
        ... )
        
        >>> # Method 2: Using string format
        >>> frames2 = MultiFrameModule.from_required_elements(
        ...     number_of_frames=10,
        ...     frame_increment_pointer=["0018,1063"]  # Frame Time tag
        ... )
        
        >>> # Method 3: Using Tag objects
        >>> from pydicom.tag import Tag
        >>> frames3 = MultiFrameModule.from_required_elements(
        ...     number_of_frames=10,
        ...     frame_increment_pointer=[Tag(0x0018, 0x1063)]
        ... )
        
        Validation and clinical use:
        
        >>> # Always validate before clinical use
        >>> result = cardiac_cine.validate()
        >>> if result.is_valid:
        ...     print("Multi-frame module ready for clinical imaging")
        ...     print(f"Frame sequencing: {cardiac_cine.get_frame_increment_attributes()}")
        ... else:
        ...     print("Validation issues found:", result.errors)
    
    See Also:
        - StereoPairsPresent enum: Defines stereoscopic imaging modes
        - MultiFrameValidator: Validates multi-frame data for DICOM compliance
        - Frame increment strategies: Frame Time, Frame Time Vector, Functional Groups
        - DICOM PS3.3 Section C.7.6.6: Complete specification reference
    """

    @classmethod
    def from_required_elements(
        cls,
        number_of_frames: int,
        frame_increment_pointer: list[int | BaseTag | str]
    ) -> 'MultiFrameModule':
        """Create MultiFrameModule with all required DICOM elements for multi-frame imaging.
        
        Creates a complete multi-frame module with all Type 1 (required) DICOM attributes.
        The Frame Increment Pointer defines how frames are sequenced and timed, enabling
        proper temporal organization for dynamic imaging and volumetric reconstruction.
        
        Args:
            number_of_frames (int): Total number of frames in the multi-frame image (0028,0008) Type 1.
                Must be greater than zero. For stereoscopic imaging, should be even (pairs).
                
            frame_increment_pointer (list[int | Tag | str]): DICOM tags defining frame sequencing (0028,0009) Type 1.
                Specifies which DICOM attributes control frame progression and timing. Supports:
                - int: Combined tag value (e.g., 0x00181063 for Frame Time)
                - Tag: pydicom Tag object (e.g., Tag(0x0018, 0x1063))
                - str: Tag in "GGGG,EEEE" format (e.g., "0018,1063") - automatically converted
            
        Returns:
            MultiFrameModule: Configured module ready for optional elements and validation.
                
        Raises:
            ValueError: If frame_increment_pointer contains invalid tag format that cannot
                be converted to proper DICOM AT (Attribute Tag) value representation.
        """
        instance = cls()
        instance._dataset.NumberOfFrames = number_of_frames
        
        # Convert frame increment pointer values to proper integer format for AT VR
        converted_tags = []
        for tag in frame_increment_pointer:
            if isinstance(tag, str):
                # Convert "GGGG,EEEE" format to integer
                if ',' in tag:
                    group_str, element_str = tag.split(',')
                    group = int(group_str, 16)
                    element = int(element_str, 16)
                    converted_tags.append((group << 16) | element)
                else:
                    # Assume it's a hex string like "00181063"
                    converted_tags.append(int(tag, 16))
            elif isinstance(tag, BaseTag):
                # Convert Tag object to integer
                converted_tags.append(int(tag))
            elif isinstance(tag, int):
                # Already in correct format
                converted_tags.append(tag)
            else:
                raise ValueError(f"Invalid frame increment pointer format: {tag}")
        
        instance._dataset.FrameIncrementPointer = converted_tags
        return instance
    
    def with_optional_elements(
        self,
        stereo_pairs_present: str | StereoPairsPresent | None = None,
        encapsulated_pixel_data_value_total_length: int | None = None
    ) -> 'MultiFrameModule':
        """Add optional elements to enhance multi-frame processing and stereoscopic capabilities.
        
        These Type 3 elements provide additional metadata for advanced multi-frame imaging
        workflows, particularly stereoscopic 3D visualization and compressed pixel data
        management.
        
        Args:
            stereo_pairs_present (str | StereoPairsPresent | None): Stereoscopic imaging mode (0022,0028) Type 3.
                Indicates whether the multi-frame dataset contains stereoscopic image pairs.
                StereoPairsPresent.YES for left/right pairs, StereoPairsPresent.NO for sequential frames.
                
            encapsulated_pixel_data_value_total_length (int | None): Total compressed data size (7FE0,0003) Type 3.
                Specifies the total byte length of pixel data when all compressed fragments
                are combined. Used for memory allocation and transfer optimization.
            
        Returns:
            MultiFrameModule: Self-reference enabling method chaining for fluent interface.
            
        Note:
            When stereoscopic pairs are enabled, ensure the number_of_frames is even and
            that frame data is properly organized as alternating left/right pairs.
        """
        if stereo_pairs_present is not None:
            self._dataset.StereoPairsPresent = format_enum_value(stereo_pairs_present)
        if encapsulated_pixel_data_value_total_length is not None:
            self._dataset.EncapsulatedPixelDataValueTotalLength = encapsulated_pixel_data_value_total_length
        return self
    
    @staticmethod
    def create_frame_increment_pointer_for_frame_time() -> list[int]:
        """Create frame increment pointer for Frame Time attribute.
        
        Returns:
            list[int]: Frame increment pointer pointing to Frame Time (0018,1063).
        """
        return [int(Tag(0x0018, 0x1063))]
    
    @staticmethod
    def create_frame_increment_pointer_for_frame_time_vector() -> list[int]:
        """Create frame increment pointer for Frame Time Vector attribute.
        
        Returns:
            list[int]: Frame increment pointer pointing to Frame Time Vector (0018,1065).
        """
        return [int(Tag(0x0018, 0x1065))]
    
    @staticmethod
    def create_frame_increment_pointer_for_functional_groups() -> list[int]:
        """Create frame increment pointer for Per-Frame Functional Groups.
        
        Returns:
            list[int]: Frame increment pointer pointing to Per-Frame Functional Groups Sequence (5200,9230).
        """
        return [int(Tag(0x5200, 0x9230))]
    
    @property
    def is_single_frame(self) -> bool:
        """Check if this is actually a single frame (edge case).
        
        Returns:
            bool: True if number of frames equals 1
        """
        return hasattr(self._dataset, 'NumberOfFrames') and self._dataset.NumberOfFrames == 1
    
    @property
    def is_multi_frame(self) -> bool:
        """Check if this is truly multi-frame.
        
        Returns:
            bool: True if number of frames is greater than 1
        """
        return hasattr(self._dataset, 'NumberOfFrames') and self._dataset.NumberOfFrames > 1
    
    @property
    def has_stereo_pairs(self) -> bool:
        """Check if stereoscopic pairs are present.
        
        Returns:
            bool: True if stereo pairs present indicator is YES
        """
        return hasattr(self._dataset, 'StereoPairsPresent') and self._dataset.StereoPairsPresent == "YES"
    
    @property
    def expected_stereo_frame_count(self) -> int | None:
        """Calculate expected number of stereo frame pairs.
        
        Returns:
            int | None: Number of stereo pairs (total frames / 2), or None if not applicable
        """
        if not self.has_stereo_pairs or not hasattr(self._dataset, 'NumberOfFrames'):
            return None
        # For stereo pairs, frames come in pairs (left/right)
        return self._dataset.NumberOfFrames // 2
    
    @property
    def uses_frame_time(self) -> bool:
        """Check if Frame Time is used for frame increment.
        
        Returns:
            bool: True if Frame Time tag is present in frame increment pointer
        """
        if not hasattr(self._dataset, 'FrameIncrementPointer'):
            return False
        frame_time_tag = Tag(0x0018, 0x1063)
        fip = self._dataset.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            return frame_time_tag in fip
        else:
            return fip == frame_time_tag
    
    @property
    def uses_frame_time_vector(self) -> bool:
        """Check if Frame Time Vector is used for frame increment.
        
        Returns:
            bool: True if Frame Time Vector tag is present in frame increment pointer
        """
        if not hasattr(self._dataset, 'FrameIncrementPointer'):
            return False
        frame_time_vector_tag = Tag(0x0018, 0x1065)
        fip = self._dataset.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            return frame_time_vector_tag in fip
        else:
            return fip == frame_time_vector_tag
    
    @property
    def uses_functional_groups(self) -> bool:
        """Check if Per-Frame Functional Groups are used for frame increment.
        
        Returns:
            bool: True if Functional Groups tag is present in frame increment pointer
        """
        if not hasattr(self._dataset, 'FrameIncrementPointer'):
            return False
        functional_groups_tag = Tag(0x5200, 0x9230)
        fip = self._dataset.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            return functional_groups_tag in fip
        else:
            return fip == functional_groups_tag
    
    def get_frame_increment_attributes(self) -> list[str]:
        """Get list of frame increment attribute names.
        
        Returns:
            list[str]: List of DICOM attribute names referenced by frame increment pointer
        """
        if not hasattr(self._dataset, 'FrameIncrementPointer'):
            return []
        
        # Map common tags to attribute names
        tag_to_attr = {
            Tag(0x0018, 0x1063): "FrameTime",
            Tag(0x0018, 0x1065): "FrameTimeVector", 
            Tag(0x5200, 0x9230): "PerFrameFunctionalGroupsSequence"
        }
        
        fip = self._dataset.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            tag_list = fip
        else:
            tag_list = [fip]
        
        attributes = []
        for tag in tag_list:
            if tag in tag_to_attr:
                attributes.append(tag_to_attr[tag])
            else:
                # For unknown tags, convert Tag back to hex format
                if hasattr(tag, 'group') and hasattr(tag, 'element'):
                    attributes.append(f"Tag{tag.group:04X}{tag.element:04X}")
                else:
                    attributes.append(f"UnknownTag_{tag}")
        
        return attributes
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate multi-frame module for DICOM PS3.3 C.7.6.6 compliance and clinical use.
        
        Performs comprehensive validation of multi-frame data to ensure compatibility with
        DICOM viewers and clinical imaging systems. Covers required elements, frame sequencing,
        stereoscopic configuration, and temporal relationships.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration to customize
                validation behavior, error reporting levels, and specific check enablement.
                If None, uses default validation settings appropriate for clinical use.
        
        Returns:
            ValidationResult: Structured validation results containing any errors or warnings.
                Contains errors list, warnings list, and is_valid boolean for overall status.
        """
        return MultiFrameValidator.validate(self._dataset, config)
