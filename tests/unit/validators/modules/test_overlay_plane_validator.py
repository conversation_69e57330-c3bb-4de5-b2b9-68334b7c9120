"""
Test OverlayPlaneValidator functionality and DICOM compliance.

Tests for DICOM PS3.3 C.9.2 Overlay Plane Module validation including
group-specific tag validation, conditional logic, and semantic validation.

BREAKING CHANGES: Tests updated for composition-based architecture where
validators receive datasets generated from modules via to_dataset() method.
"""

import pytest
import numpy as np
from pydicom import Dataset
from pydicom.tag import Tag
from pyrt_dicom.validators.modules.overlay_plane_validator import OverlayPlaneValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums.image_enums import OverlayType, OverlaySubtype
from pyrt_dicom.modules import OverlayPlaneModule


class TestOverlayPlaneValidator:
    """Test OverlayPlaneValidator functionality and DICOM compliance."""
    
    def test_validate_returns_validation_result(self):
        """Test that validate method returns proper ValidationResult instance."""
        dataset = Dataset()
        
        result = OverlayPlaneValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_empty_dataset_fails_validation(self):
        """Test that empty dataset fails validation (no overlay groups found)."""
        dataset = Dataset()
        
        result = OverlayPlaneValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert len(result.errors) == 1
        assert "No overlay plane elements found in dataset" in result.errors[0]
    
    def test_valid_graphics_overlay_passes_validation(self):
        """Test that valid graphics overlay passes validation."""
        # Create overlay using module and extract dataset
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        overlay_data[10:22, 10:22] = 1
        
        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type=OverlayType.GRAPHICS,
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        )
        
        dataset = overlay.to_dataset()
        result = OverlayPlaneValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_valid_roi_overlay_with_statistics_passes_validation(self):
        """Test that valid ROI overlay with statistics passes validation."""
        overlay_data = np.zeros((64, 64), dtype=np.uint8)
        overlay_data[20:44, 20:44] = 1  # 24x24 ROI
        
        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=64,
            overlay_columns=64,
            overlay_type=OverlayType.ROI,
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        ).with_roi_statistics(
            roi_area=576,  # 24x24 = 576 pixels
            roi_mean=150.5,
            roi_standard_deviation=12.3
        )
        
        dataset = overlay.to_dataset()
        result = OverlayPlaneValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_missing_required_elements_fails_validation(self):
        """Test that missing required elements fails validation."""
        dataset = Dataset()
        # Add only partial overlay elements to trigger Type 1 validation
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)  # OverlayRows only
        
        result = OverlayPlaneValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have errors for missing required elements
        assert len(result.errors) >= 6  # Missing 6 other required elements
    
    def test_invalid_overlay_type_fails_validation(self):
        """Test that invalid overlay type fails enumerated value validation."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        # Create dataset manually with invalid overlay type
        dataset = Dataset()
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'X')  # Invalid type
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())
        
        result = OverlayPlaneValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have error for invalid overlay type
        error_found = any("Overlay Type" in error and "must be one of" in error 
                         for error in result.errors)
        assert error_found
    
    def test_invalid_overlay_bits_allocated_fails_validation(self):
        """Test that invalid overlay bits allocated fails validation."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        # Create dataset with invalid bits allocated
        dataset = Dataset()
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'G')
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 8)  # Invalid - should be 1
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())
        
        result = OverlayPlaneValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have error for invalid bits allocated
        error_found = any("Overlay Bits Allocated" in error and "must be 1" in error 
                         for error in result.errors)
        assert error_found
    
    def test_invalid_overlay_bit_position_fails_validation(self):
        """Test that invalid overlay bit position fails validation."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        # Create dataset with invalid bit position
        dataset = Dataset()
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'G')
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 3)  # Invalid - should be 0
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())
        
        result = OverlayPlaneValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have error for invalid bit position
        error_found = any("Overlay Bit Position" in error and "must be 0" in error 
                         for error in result.errors)
        assert error_found
    
    def test_insufficient_overlay_data_fails_validation(self):
        """Test that insufficient overlay data fails validation."""
        overlay_data = np.zeros((16, 16), dtype=np.uint8)  # Too small for 32x32
        
        # Create dataset with insufficient data
        dataset = Dataset()
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)  # 32 rows
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)  # 32 columns
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'G')
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())  # Too small
        
        result = OverlayPlaneValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have error for insufficient data size
        error_found = any("Overlay Data" in error and "size" in error and "less than required" in error 
                         for error in result.errors)
        assert error_found
    
    def test_roi_statistics_with_graphics_overlay_generates_warning(self):
        """Test that ROI statistics with graphics overlay generates warning."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type=OverlayType.GRAPHICS,  # Graphics, not ROI
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        ).with_roi_statistics(
            roi_area=100,  # ROI stats on graphics overlay
            roi_mean=128.0,
            roi_standard_deviation=15.0
        )
        
        dataset = overlay.to_dataset()
        result = OverlayPlaneValidator.validate(dataset)
        
        # Should pass validation but generate warning
        assert result.is_valid  # No errors
        assert result.has_warnings
        # Should have warning about ROI stats on graphics overlay
        warning_found = any("ROI statistics are present" in warning and "not 'R' (ROI)" in warning 
                           for warning in result.warnings)
        assert warning_found
    
    def test_negative_roi_area_fails_validation(self):
        """Test that negative ROI area fails validation."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        # Create dataset with negative ROI area
        dataset = Dataset()
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'R')
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())
        dataset.add_new(Tag(0x6000, 0x1301), 'IS', '-100')  # Negative ROI area
        
        result = OverlayPlaneValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have error for negative ROI area
        error_found = any("ROI Area" in error and "must be non-negative" in error 
                         for error in result.errors)
        assert error_found
    
    def test_validation_config_controls_enumerated_value_checking(self):
        """Test that ValidationConfig controls enumerated value checking."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        # Create dataset with invalid overlay type
        dataset = Dataset()
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'X')  # Invalid type
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())
        
        # Test with enumerated value checking disabled
        config = ValidationConfig(check_enumerated_values=False)
        result = OverlayPlaneValidator.validate(dataset, config)
        
        # Should not have enumerated value errors when checking is disabled
        enum_error_found = any("Overlay Type" in error and "must be one of" in error 
                              for error in result.errors)
        assert not enum_error_found
        
        # Test with enumerated value checking enabled (default)
        config = ValidationConfig(check_enumerated_values=True)
        result = OverlayPlaneValidator.validate(dataset, config)
        
        # Should have enumerated value errors when checking is enabled
        enum_error_found = any("Overlay Type" in error and "must be one of" in error 
                              for error in result.errors)
        assert enum_error_found
    
    def test_multiple_overlay_groups_validation(self):
        """Test validation of multiple overlay groups in same dataset."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        # Create dataset with two overlay groups
        dataset = Dataset()
        
        # First overlay group (6000)
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'G')
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())
        
        # Second overlay group (6002) with error
        dataset.add_new(Tag(0x6002, 0x0010), 'US', 32)
        dataset.add_new(Tag(0x6002, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6002, 0x0040), 'CS', 'X')  # Invalid type
        dataset.add_new(Tag(0x6002, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6002, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6002, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6002, 0x3000), 'OW', overlay_data.tobytes())
        
        result = OverlayPlaneValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have error for invalid overlay type in group 6002
        error_found = any("6002" in error and "Overlay Type" in error
                         for error in result.errors)
        assert error_found

    def test_multi_frame_overlay_conditional_logic_validation(self):
        """Test multi-frame overlay conditional logic validation."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)

        # Test overlay with multi-frame elements
        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type=OverlayType.GRAPHICS,
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        ).with_multi_frame_overlay(
            number_of_frames_in_overlay=3,
            image_frame_origin=[1, 5, 10]
        )

        dataset = overlay.to_dataset()
        result = OverlayPlaneValidator.validate(dataset)

        assert result.is_valid
        assert not result.has_errors

    def test_incomplete_multi_frame_elements_generates_warning(self):
        """Test that incomplete multi-frame elements generate warnings."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)

        # Create dataset with only Number of Frames, missing Image Frame Origin
        dataset = Dataset()
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'G')
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())
        dataset.add_new(Tag(0x6000, 0x0015), 'IS', '3')  # Number of Frames only

        result = OverlayPlaneValidator.validate(dataset)

        # Should pass validation but generate warning
        assert result.is_valid
        assert result.has_warnings
        # Should have warning about incomplete multi-frame elements
        warning_found = any("Number of Frames in Overlay" in warning and "missing" in warning
                           for warning in result.warnings)
        assert warning_found

    def test_invalid_frame_count_fails_validation(self):
        """Test that invalid frame count fails validation."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)

        # Create dataset with invalid frame count
        dataset = Dataset()
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'G')
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())
        dataset.add_new(Tag(0x6000, 0x0015), 'IS', '0')  # Invalid frame count
        dataset.add_new(Tag(0x6000, 0x0051), 'IS', ['1'])

        result = OverlayPlaneValidator.validate(dataset)

        assert not result.is_valid
        assert result.has_errors
        # Should have error for invalid frame count
        error_found = any("Number of Frames in Overlay" in error and "must be positive" in error
                         for error in result.errors)
        assert error_found

    def test_invalid_frame_origin_fails_validation(self):
        """Test that invalid frame origin fails validation."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)

        # Create dataset with invalid frame origin (0-based instead of 1-based)
        dataset = Dataset()
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'G')
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())
        dataset.add_new(Tag(0x6000, 0x0015), 'IS', '3')
        dataset.add_new(Tag(0x6000, 0x0051), 'IS', ['0', '1', '2'])  # Invalid 0-based

        result = OverlayPlaneValidator.validate(dataset)

        assert not result.is_valid
        assert result.has_errors
        # Should have error for invalid frame origin
        error_found = any("Image Frame Origin" in error and "must be >= 1" in error
                         for error in result.errors)
        assert error_found

    def test_active_image_area_subtype_semantic_validation(self):
        """Test semantic validation for ACTIVE_IMAGE_AREA subtype."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)

        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type=OverlayType.ROI,  # Appropriate for active image area
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        ).with_optional_elements(
            overlay_subtype=OverlaySubtype.ACTIVE_IMAGE_AREA
        )

        dataset = overlay.to_dataset()
        result = OverlayPlaneValidator.validate(dataset)

        assert result.is_valid
        assert not result.has_errors
        # May have informational messages about active image area usage

    def test_active_image_area_with_graphics_generates_warning(self):
        """Test that ACTIVE_IMAGE_AREA with graphics overlay generates warning."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)

        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type=OverlayType.GRAPHICS,  # Not typical for active image area
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        ).with_optional_elements(
            overlay_subtype=OverlaySubtype.ACTIVE_IMAGE_AREA
        )

        dataset = overlay.to_dataset()
        result = OverlayPlaneValidator.validate(dataset)

        # Should pass validation but generate warning
        assert result.is_valid
        assert result.has_warnings
        # Should have warning about active image area with graphics
        warning_found = any("ACTIVE_IMAGE_AREA" in warning and "typically used with ROI" in warning
                           for warning in result.warnings)
        assert warning_found

    def test_roi_area_exceeds_overlay_size_generates_warning(self):
        """Test that ROI area exceeding overlay size generates warning."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)

        # Create dataset with ROI area larger than overlay
        dataset = Dataset()
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)  # 32x32 = 1024 pixels max
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'R')
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())
        dataset.add_new(Tag(0x6000, 0x1301), 'IS', '2000')  # Exceeds 1024

        result = OverlayPlaneValidator.validate(dataset)

        # Should pass validation but generate warning
        assert result.is_valid
        assert result.has_warnings
        # Should have warning about ROI area exceeding overlay size
        warning_found = any("ROI Area" in warning and "exceeds total overlay area" in warning
                           for warning in result.warnings)
        assert warning_found
