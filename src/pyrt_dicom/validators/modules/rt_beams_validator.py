"""RT Beams Module DICOM validation - PS3.3 C.8.8.14

Comprehensive validation for RT Beams Module implementing all conditional logic
and sequence requirements according to DICOM PS3.3 specification.
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.rt_enums import (
    RTBeamLimitingDeviceType,
    PrimaryDosimeterUnit, 
    EnhancedRTBeamLimitingDeviceDefinitionFlag
)
from ...enums import BeamType, RadiationType


class RTBeamsValidator(BaseValidator):
    """Validator for DICOM RT Beams Module (PS3.3 C.8.8.14).
    
    Implements comprehensive validation including:
    - All Type 1/2/3/1C/2C requirements
    - Enhanced RT Beam Limiting Device conditional logic
    - Number-based sequence requirements (wedges, compensators, boli, blocks)
    - Control Point sequence validation
    - Material ID conditional requirements for compensators and blocks
    - Sequence structure and cross-field validation
    - Enumerated value validation
    - DICOM compliance and consistency checks
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate RT Beams Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Always validate core requirements (Type 1 elements)
        RTBeamsValidator._validate_type_1_requirements(dataset, result)
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            RTBeamsValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            RTBeamsValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            RTBeamsValidator._validate_sequence_requirements(dataset, result)
        
        # Validate beam parameter consistency
        RTBeamsValidator._validate_beam_parameter_consistency(dataset, result)
        
        # Validate material ID conditional requirements
        if config.validate_conditional_requirements:
            RTBeamsValidator._validate_material_id_requirements(dataset, result)
        
        # Validate control point sequence requirements
        if config.validate_sequences:
            RTBeamsValidator._validate_control_point_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_type_1_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 (required) elements."""
        # Beam Sequence (300A,00B0) Type 1
        beam_sequence = getattr(dataset, 'BeamSequence', None)
        if beam_sequence is None:
            result.add_error(
                'Beam Sequence (300A,00B0) is required (Type 1) but is missing'
            )
            return
        
        if len(beam_sequence) == 0:
            result.add_error(
                'Beam Sequence (300A,00B0) must contain one or more Items'
            )
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate comprehensive Type 1C conditional requirements."""
        
        beam_sequence = getattr(dataset, 'BeamSequence', [])
        for i, beam_item in enumerate(beam_sequence):
            RTBeamsValidator._validate_enhanced_device_flag_requirements(beam_item, i, result)
            RTBeamsValidator._validate_number_based_sequence_requirements(beam_item, i, result)
            RTBeamsValidator._validate_high_dose_technique_requirements(beam_item, i, result)
            RTBeamsValidator._validate_final_cumulative_weight_requirements(beam_item, i, result)
    
    @staticmethod
    def _validate_enhanced_device_flag_requirements(beam_item: Dataset, beam_index: int, result: ValidationResult) -> None:
        """Validate Enhanced RT Beam Limiting Device Definition Flag conditional requirements."""
        enhanced_flag = beam_item.get('EnhancedRTBeamLimitingDeviceDefinitionFlag', '')
        
        # Type 1C: Beam Limiting Device Sequence required if Enhanced flag is absent or NO
        if enhanced_flag != 'YES':
            if not beam_item.get('BeamLimitingDeviceSequence'):
                result.add_error(
                    f'Beam Sequence item {beam_index}: '
                    'Beam Limiting Device Sequence (300A,00B6) is required when '
                    'Enhanced RT Beam Limiting Device Definition Flag is absent or NO'
                )
        
        # Type 1C: Enhanced RT Beam Limiting Device Sequence required if Enhanced flag is YES
        if enhanced_flag == 'YES':
            if not beam_item.get('EnhancedRTBeamLimitingDeviceSequence'):
                result.add_error(
                    f'Beam Sequence item {beam_index}: '
                    'Enhanced RT Beam Limiting Device Sequence (3008,00A1) is required when '
                    'Enhanced RT Beam Limiting Device Definition Flag is YES'
                )
    
    @staticmethod
    def _validate_number_based_sequence_requirements(beam_item: Dataset, beam_index: int, result: ValidationResult) -> None:
        """Validate number-based conditional sequence requirements."""
        # Number of Wedges → Wedge Sequence requirement
        number_of_wedges = beam_item.get('NumberOfWedges', 0)
        if number_of_wedges > 0 and not beam_item.get('WedgeSequence'):
            result.add_error(
                f'Beam Sequence item {beam_index}: '
                f'Wedge Sequence (300A,00D1) is required when '
                f'Number of Wedges ({number_of_wedges}) is non-zero'
            )
        
        # Number of Compensators → Compensator Sequence requirement
        number_of_compensators = beam_item.get('NumberOfCompensators', 0)
        if number_of_compensators > 0 and not beam_item.get('CompensatorSequence'):
            result.add_error(
                f'Beam Sequence item {beam_index}: '
                f'Compensator Sequence (300A,00E3) is required when '
                f'Number of Compensators ({number_of_compensators}) is non-zero'
            )
        
        # Number of Boli → Referenced Bolus Sequence requirement
        number_of_boli = beam_item.get('NumberOfBoli', 0)
        if number_of_boli > 0 and not beam_item.get('ReferencedBolusSequence'):
            result.add_error(
                f'Beam Sequence item {beam_index}: '
                f'Referenced Bolus Sequence (300C,00B0) is required when '
                f'Number of Boli ({number_of_boli}) is non-zero'
            )
        
        # Number of Blocks → Block Sequence requirement
        number_of_blocks = beam_item.get('NumberOfBlocks', 0)
        if number_of_blocks > 0 and not beam_item.get('BlockSequence'):
            result.add_error(
                f'Beam Sequence item {beam_index}: '
                f'Block Sequence (300A,00F4) is required when '
                f'Number of Blocks ({number_of_blocks}) is non-zero'
            )
    
    @staticmethod
    def _validate_high_dose_technique_requirements(beam_item: Dataset, beam_index: int, result: ValidationResult) -> None:
        """Validate High-Dose Technique Type conditional requirements."""
        # Type 1C: High-Dose Technique Type required if treatment technique requires dose override
        # This is difficult to validate automatically, so we provide guidance
        high_dose_type = beam_item.get('HighDoseTechniqueType')
        if high_dose_type:
            valid_types = ['TBI', 'HDR']
            if high_dose_type not in valid_types:
                result.add_warning(
                    f'Beam Sequence item {beam_index}: '
                    f'High-Dose Technique Type ({high_dose_type}) should be one of: {", ".join(valid_types)}'
                )
    
    @staticmethod
    def _validate_final_cumulative_weight_requirements(beam_item: Dataset, beam_index: int, result: ValidationResult) -> None:
        """Validate Final Cumulative Meterset Weight conditional requirements."""
        # Type 1C: Final Cumulative Meterset Weight required if Cumulative Meterset Weight is non-null in Control Points
        control_point_sequence = beam_item.get('ControlPointSequence', [])
        has_non_null_cumulative_weights = any(
            cp.get('CumulativeMetersetWeight') is not None 
            for cp in control_point_sequence
        )
        
        if has_non_null_cumulative_weights:
            final_weight = beam_item.get('FinalCumulativeMetersetWeight')
            if final_weight is None:
                result.add_error(
                    f'Beam Sequence item {beam_index}: '
                    'Final Cumulative Meterset Weight (300A,010E) is required when '
                    'Cumulative Meterset Weight is non-null in Control Points'
                )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications."""
        
        beam_sequence = getattr(dataset, 'BeamSequence', [])
        for i, beam_item in enumerate(beam_sequence):
            # Beam Type (300A,00C4)
            beam_type = beam_item.get('BeamType', '')
            if beam_type:
                valid_beam_types = [btype.value for btype in BeamType]
                BaseValidator.validate_enumerated_value(
                    beam_type, valid_beam_types,
                    f"Beam Type (300A,00C4) in Beam {i}", result
                )
            
            # Radiation Type (300A,00C6)
            radiation_type = beam_item.get('RadiationType', '')
            if radiation_type:
                valid_radiation_types = [rtype.value for rtype in RadiationType]
                BaseValidator.validate_enumerated_value(
                    radiation_type, valid_radiation_types,
                    f"Radiation Type (300A,00C6) in Beam {i}", result
                )
            
            # Primary Dosimeter Unit (300A,00B3)
            dosimeter_unit = beam_item.get('PrimaryDosimeterUnit', '')
            if dosimeter_unit:
                valid_dosimeter_units = [unit.value for unit in PrimaryDosimeterUnit]
                BaseValidator.validate_enumerated_value(
                    dosimeter_unit, valid_dosimeter_units,
                    f"Primary Dosimeter Unit (300A,00B3) in Beam {i}", result
                )
            
            # Enhanced RT Beam Limiting Device Definition Flag (3008,00A3)
            enhanced_flag = beam_item.get('EnhancedRTBeamLimitingDeviceDefinitionFlag', '')
            if enhanced_flag:
                valid_flags = [flag.value for flag in EnhancedRTBeamLimitingDeviceDefinitionFlag]
                BaseValidator.validate_enumerated_value(
                    enhanced_flag, valid_flags,
                    f'Enhanced RT Beam Limiting Device Definition Flag (3008,00A3) in Beam {i}', result
                )
            
            # Validate Beam Limiting Device Sequence enumerated values
            bld_sequence = beam_item.get('BeamLimitingDeviceSequence', [])
            for j, bld_item in enumerate(bld_sequence):
                device_type = bld_item.get('RTBeamLimitingDeviceType', '')
                if device_type:
                    valid_device_types = [dtype.value for dtype in RTBeamLimitingDeviceType]
                    BaseValidator.validate_enumerated_value(
                        device_type, valid_device_types,
                        f'RT Beam Limiting Device Type (300A,00B8) in Device {j}, Beam {i}', result
                    )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate comprehensive sequence structure requirements."""
        beam_sequence = getattr(dataset, 'BeamSequence', [])
        
        for i, beam_item in enumerate(beam_sequence):
            RTBeamsValidator._validate_beam_item_requirements(beam_item, i, result)
            RTBeamsValidator._validate_beam_limiting_device_sequences(beam_item, i, result)
            RTBeamsValidator._validate_wedge_compensator_sequences(beam_item, i, result)
            RTBeamsValidator._validate_block_bolus_sequences(beam_item, i, result)
        
        # Validate uniqueness of beam numbers
        RTBeamsValidator._validate_beam_number_uniqueness(beam_sequence, result)
    
    @staticmethod
    def _validate_beam_item_requirements(beam_item: Dataset, beam_index: int, result: ValidationResult) -> None:
        """Validate individual beam item required elements."""
        # Type 1 Elements
        required_type1_elements = [
            ('BeamNumber', '300A,00C0'),
            ('BeamType', '300A,00C4'),
            ('NumberOfWedges', '300A,00D0'),
            ('NumberOfCompensators', '300A,00E0'), 
            ('NumberOfBoli', '300A,00ED'),
            ('NumberOfBlocks', '300A,00F0'),
            ('NumberOfControlPoints', '300A,0110'),
            ('ControlPointSequence', '300A,0111')
        ]
        
        for element_name, tag in required_type1_elements:
            if element_name not in beam_item or beam_item.get(element_name) is None:
                result.add_error(
                    f'Beam Sequence item {beam_index}: '
                    f'{element_name} ({tag}) is required (Type 1)'
                )
        
        # Type 2 Elements (must be present but can be empty)
        required_type2_elements = [
            ('TreatmentMachineName', '300A,00B2'),
            ('RadiationType', '300A,00C6')
        ]
        
        for element_name, tag in required_type2_elements:
            if element_name not in beam_item:
                result.add_error(
                    f'Beam Sequence item {beam_index}: '
                    f'{element_name} ({tag}) is required (Type 2)'
                )
    
    @staticmethod
    def _validate_beam_limiting_device_sequences(beam_item: Dataset, beam_index: int, result: ValidationResult) -> None:
        """Validate beam limiting device sequence requirements."""
        # Validate standard Beam Limiting Device Sequence
        bld_sequence = beam_item.get('BeamLimitingDeviceSequence', [])
        for j, bld_item in enumerate(bld_sequence):
            if not bld_item.get('RTBeamLimitingDeviceType'):
                result.add_error(
                    f'Beam Limiting Device Sequence item {j} in Beam {beam_index}: '
                    'RT Beam Limiting Device Type (300A,00B8) is required'
                )
            if not bld_item.get('NumberOfLeafJawPairs'):
                result.add_error(
                    f'Beam Limiting Device Sequence item {j} in Beam {beam_index}: '
                    'Number of Leaf/Jaw Pairs (300A,00BC) is required'
                )
            
            # Type 2C: Leaf Position Boundaries required for MLCX/MLCY
            device_type = bld_item.get('RTBeamLimitingDeviceType', '')
            if device_type in ['MLCX', 'MLCY'] and not bld_item.get('LeafPositionBoundaries'):
                result.add_error(
                    f'Beam Limiting Device Sequence item {j} in Beam {beam_index}: '
                    f'Leaf Position Boundaries (300A,00BE) is required when '
                    f'RT Beam Limiting Device Type is {device_type}'
                )
    
    @staticmethod
    def _validate_wedge_compensator_sequences(beam_item: Dataset, beam_index: int, result: ValidationResult) -> None:
        """Validate wedge and compensator sequence requirements."""
        # Validate Wedge Sequence
        wedge_sequence = beam_item.get('WedgeSequence', [])
        for j, wedge_item in enumerate(wedge_sequence):
            if not wedge_item.get('WedgeNumber'):
                result.add_error(
                    f'Wedge Sequence item {j} in Beam {beam_index}: '
                    'Wedge Number (300A,00D2) is required'
                )
            # Type 2 elements for wedges
            wedge_type2_elements = ['WedgeType', 'WedgeAngle', 'WedgeFactor', 'WedgeOrientation']
            for element in wedge_type2_elements:
                if element not in wedge_item:
                    result.add_error(
                        f'Wedge Sequence item {j} in Beam {beam_index}: '
                        f'{element} is required (Type 2)'
                    )
        
        # Validate Compensator Sequence
        compensator_sequence = beam_item.get('CompensatorSequence', [])
        for j, comp_item in enumerate(compensator_sequence):
            required_comp_elements = ['CompensatorRows', 'CompensatorColumns']
            for element in required_comp_elements:
                if not comp_item.get(element):
                    result.add_error(
                        f'Compensator Sequence item {j} in Beam {beam_index}: '
                        f'{element} is required'
                    )
    
    @staticmethod
    def _validate_block_bolus_sequences(beam_item: Dataset, beam_index: int, result: ValidationResult) -> None:
        """Validate block and bolus sequence requirements."""
        # Validate Block Sequence
        block_sequence = beam_item.get('BlockSequence', [])
        for j, block_item in enumerate(block_sequence):
            block_required_elements = [
                ('BlockNumber', '300A,00FC'),
                ('BlockType', '300A,00F8')
            ]
            for element_name, tag in block_required_elements:
                if not block_item.get(element_name):
                    result.add_error(
                        f'Block Sequence item {j} in Beam {beam_index}: '
                        f'{element_name} ({tag}) is required'
                    )
        
        # Validate Referenced Bolus Sequence
        bolus_sequence = beam_item.get('ReferencedBolusSequence', [])
        for j, bolus_item in enumerate(bolus_sequence):
            if not bolus_item.get('ReferencedROINumber'):
                result.add_error(
                    f'Referenced Bolus Sequence item {j} in Beam {beam_index}: '
                    'Referenced ROI Number (3006,0084) is required'
                )
    
    @staticmethod
    def _validate_beam_number_uniqueness(beam_sequence: list, result: ValidationResult) -> None:
        """Validate uniqueness of beam numbers within the RT Plan."""
        beam_numbers = []
        for i, beam_item in enumerate(beam_sequence):
            beam_number = beam_item.get('BeamNumber')
            if beam_number is not None:
                if beam_number in beam_numbers:
                    result.add_error(
                        f'Beam Sequence item {i}: '
                        f'Beam Number ({beam_number}) must be unique within the RT Plan'
                    )
                else:
                    beam_numbers.append(beam_number)
    
    @staticmethod
    def _validate_beam_parameter_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate beam parameter consistency and logical constraints."""
        
        beam_sequence = getattr(dataset, 'BeamSequence', [])
        for i, beam_item in enumerate(beam_sequence):
            # Validate Source Axis Distance is positive
            source_axis_distance = beam_item.get('SourceAxisDistance')
            if source_axis_distance is not None and source_axis_distance <= 0:
                result.add_warning(
                    f"Beam Sequence item {i}: "
                    f"Source Axis Distance ({source_axis_distance}) should be positive"
                )
            
            # Validate Beam Limiting Device Sequence consistency
            bld_sequence = beam_item.get('BeamLimitingDeviceSequence', [])
            for j, bld_item in enumerate(bld_sequence):
                # Validate Source to Beam Limiting Device Distance is positive
                source_to_bld_distance = bld_item.get('SourceToBeamLimitingDeviceDistance')
                if source_to_bld_distance is not None and source_to_bld_distance <= 0:
                    result.add_warning(
                        f"Beam Limiting Device Sequence item {j} in Beam {i}: "
                        f"Source to Beam Limiting Device Distance ({source_to_bld_distance}) should be positive"
                    )
                
                # Validate Number of Leaf/Jaw Pairs is positive
                num_pairs = bld_item.get('NumberOfLeafJawPairs', 0)
                if num_pairs <= 0:
                    result.add_warning(
                        f"Beam Limiting Device Sequence item {j} in Beam {i}: "
                        f"Number of Leaf/Jaw Pairs ({num_pairs}) should be positive"
                    )
                
                # Validate Leaf Position Boundaries consistency
                leaf_boundaries = bld_item.get('LeafPositionBoundaries', [])
                if leaf_boundaries:
                    expected_length = num_pairs + 1
                    if len(leaf_boundaries) != expected_length:
                        result.add_error(
                            f"Beam Limiting Device Sequence item {j} in Beam {i}: "
                            f"Leaf Position Boundaries length ({len(leaf_boundaries)}) should be "
                            f"Number of Leaf/Jaw Pairs + 1 ({expected_length})"
                        )
                    
                    # Check that boundaries are in ascending order
                    if len(leaf_boundaries) > 1:
                        for k in range(1, len(leaf_boundaries)):
                            if leaf_boundaries[k] <= leaf_boundaries[k-1]:
                                result.add_warning(
                                    f'Beam Limiting Device Sequence item {j} in Beam {i}: '
                                    f'Leaf Position Boundaries should be in ascending order'
                                )
                                break

    @staticmethod
    def _validate_material_id_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Material ID conditional requirements for compensators and blocks."""
        beam_sequence = getattr(dataset, 'BeamSequence', [])
        
        for i, beam_item in enumerate(beam_sequence):
            # Validate compensator material ID requirements
            compensator_sequence = beam_item.get('CompensatorSequence', [])
            for j, comp_item in enumerate(compensator_sequence):
                material_id = comp_item.get('MaterialID', '')
                transmission_data = comp_item.get('CompensatorTransmissionData')
                thickness_data = comp_item.get('CompensatorThicknessData')
                
                if material_id == '' and not transmission_data:
                    result.add_error(
                        f'Compensator Sequence item {j} in Beam {i}: '
                        'Compensator Transmission Data (300A,00EB) is required when Material ID is zero-length'
                    )
                
                if material_id != '' and not thickness_data:
                    result.add_error(
                        f'Compensator Sequence item {j} in Beam {i}: '
                        'Compensator Thickness Data (300A,00EC) is required when Material ID is non-zero length'
                    )
            
            # Validate block material ID requirements
            block_sequence = beam_item.get('BlockSequence', [])
            for j, block_item in enumerate(block_sequence):
                material_id = block_item.get('MaterialID', '')
                transmission = block_item.get('BlockTransmission')
                thickness = block_item.get('BlockThickness')
                
                if material_id == '' and not transmission:
                    result.add_error(
                        f'Block Sequence item {j} in Beam {i}: '
                        'Block Transmission (300A,0102) is required when Material ID is zero-length'
                    )
                
                if material_id != '' and not thickness:
                    result.add_error(
                        f'Block Sequence item {j} in Beam {i}: '
                        'Block Thickness (300A,0100) is required when Material ID is non-zero length'
                    )
    
    @staticmethod
    def _validate_control_point_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Control Point Sequence requirements."""
        beam_sequence = getattr(dataset, 'BeamSequence', [])
        
        for i, beam_item in enumerate(beam_sequence):
            control_point_seq = beam_item.get('ControlPointSequence', [])
            number_of_control_points = beam_item.get('NumberOfControlPoints', 0)
            
            # Validate control point count matches declared number
            if len(control_point_seq) != number_of_control_points:
                result.add_error(
                    f'Beam Sequence item {i}: '
                    f'Control Point Sequence contains {len(control_point_seq)} items but '
                    f'Number of Control Points declares {number_of_control_points}'
                )
            
            # Validate each control point
            for j, cp_item in enumerate(control_point_seq):
                RTBeamsValidator._validate_individual_control_point(cp_item, j, i, result)
            
            # Validate final cumulative meterset weight consistency
            RTBeamsValidator._validate_final_weight_consistency(beam_item, control_point_seq, i, result)
    
    @staticmethod
    def _validate_individual_control_point(cp_item: Dataset, cp_index: int, beam_index: int, result: ValidationResult) -> None:
        """Validate individual control point requirements."""
        # Control Point Index (Type 1)
        control_point_index = cp_item.get('ControlPointIndex')
        if control_point_index is None:
            result.add_error(
                f'Control Point Sequence item {cp_index} in Beam {beam_index}: '
                'Control Point Index (300A,0112) is required'
            )
        elif control_point_index != cp_index:
            result.add_error(
                f'Control Point Sequence item {cp_index} in Beam {beam_index}: '
                f'Control Point Index ({control_point_index}) should be {cp_index} (sequential)'
            )
        
        # Cumulative Meterset Weight (Type 2)
        if 'CumulativeMetersetWeight' not in cp_item:
            result.add_error(
                f'Control Point Sequence item {cp_index} in Beam {beam_index}: '
                'Cumulative Meterset Weight (300A,0134) is required (Type 2)'
            )
        
        # Validate first control point requirements (many Type 1C elements)
        if cp_index == 0:
            RTBeamsValidator._validate_first_control_point_requirements(cp_item, beam_index, result)
    
    @staticmethod
    def _validate_first_control_point_requirements(cp_item: Dataset, beam_index: int, result: ValidationResult) -> None:
        """Validate first control point specific requirements."""
        # First control point must have Cumulative Meterset Weight = 0
        cumulative_weight = cp_item.get('CumulativeMetersetWeight')
        if cumulative_weight is not None and cumulative_weight != 0:
            result.add_warning(
                f'Control Point 0 in Beam {beam_index}: '
                f'Cumulative Meterset Weight should be 0 for first control point, found {cumulative_weight}'
            )
        
        # Many elements are Type 1C for first control point or if values change
        # This validation is complex and would require comparing control points
        # For now, we provide basic validation
    
    @staticmethod
    def _validate_final_weight_consistency(beam_item: Dataset, control_point_seq: list, beam_index: int, result: ValidationResult) -> None:
        """Validate final cumulative meterset weight consistency."""
        final_weight = beam_item.get('FinalCumulativeMetersetWeight')
        if final_weight is not None and control_point_seq:
            last_cp_weight = control_point_seq[-1].get('CumulativeMetersetWeight')
            if last_cp_weight is not None and abs(final_weight - last_cp_weight) > 0.001:
                result.add_warning(
                    f'Beam Sequence item {beam_index}: '
                    f'Final Cumulative Meterset Weight ({final_weight}) should match '
                    f'last control point weight ({last_cp_weight})'
                )
