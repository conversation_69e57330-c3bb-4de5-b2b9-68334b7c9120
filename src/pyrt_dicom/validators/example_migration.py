"""Example showing migration from dict-based to class-based validation.

This module demonstrates how to migrate existing validators from the 
dict-based validation pattern to the new ValidationResult class-based pattern.
"""

from pydicom import Dataset
from .validation_result import ValidationResult
from .modules.base_validator import ValidationConfig


class ExampleValidator(BaseValidator):
    """Example validator showing both old and new patterns.
    
    This class demonstrates the migration path from dict-based validation
    results to the new ValidationResult class-based approach.
    """
    
    @staticmethod
    def validate_old_style(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Old-style validation using dict-based results.
        
        This method shows the existing pattern used throughout the codebase.
        It directly manipulates a dictionary with 'errors' and 'warnings' keys.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Example validation logic using old pattern
        if not hasattr(dataset, 'PatientID'):
            result["errors"].append("Patient ID (0010,0020) is required")
        
        if not hasattr(dataset, 'PatientName'):
            result["warnings"].append("Patient Name (0010,0010) is recommended")
        
        if hasattr(dataset, 'PatientSex'):
            if dataset.PatientSex not in ["M", "F", "O"]:
                result["errors"].append(
                    f"Patient Sex (0010,0040) has invalid value '{dataset.PatientSex}'. "
                    "Valid values: M, F, O"
                )
        
        return result
    
    @staticmethod
    def validate_new_style(dataset: Dataset, config: ValidationConfig = None) -> ValidationResult:
        """New-style validation using ValidationResult class.
        
        This method shows the recommended new pattern using the ValidationResult class.
        It provides better type safety, cleaner code, and more functionality.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult instance with errors and warnings
        """
        config = config or ValidationConfig()
        result = ValidationResult()
        
        # Example validation logic using new pattern
        if not hasattr(dataset, 'PatientID'):
            result.add_error("Patient ID (0010,0020) is required")
        
        if not hasattr(dataset, 'PatientName'):
            result.add_warning("Patient Name (0010,0010) is recommended")
        
        if hasattr(dataset, 'PatientSex'):
            if dataset.PatientSex not in ["M", "F", "O"]:
                result.add_error(
                    f"Patient Sex (0010,0040) has invalid value '{dataset.PatientSex}'. "
                    "Valid values: M, F, O"
                )
        
        return result
    
    @staticmethod
    def validate_hybrid_style(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Hybrid validation using ValidationResult internally but returning dict.
        
        This method shows a migration strategy where you can use ValidationResult
        internally for better code organization while maintaining backward compatibility
        by returning the traditional dict format.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists (backward compatible)
        """
        config = config or ValidationConfig()
        result = ValidationResult()
        
        # Use ValidationResult for cleaner internal logic
        if not hasattr(dataset, 'PatientID'):
            result.add_error("Patient ID (0010,0020) is required")
        
        if not hasattr(dataset, 'PatientName'):
            result.add_warning("Patient Name (0010,0010) is recommended")
        
        if hasattr(dataset, 'PatientSex'):
            if dataset.PatientSex not in ["M", "F", "O"]:
                result.add_error(
                    f"Patient Sex (0010,0040) has invalid value '{dataset.PatientSex}'. "
                    "Valid values: M, F, O"
                )
        
        # Return dict for backward compatibility
        return result.to_dict()


def demonstrate_migration():
    """Demonstrate the migration patterns."""
    
    # Create test dataset
    dataset = Dataset()
    dataset.PatientSex = "X"  # Invalid value
    
    print("=== Migration Demonstration ===\n")
    
    # Old style
    print("1. Old Style (dict-based):")
    old_result = ExampleValidator.validate_old_style(dataset)
    print(f"   Type: {type(old_result)}")
    print(f"   Result: {old_result}")
    print(f"   Has errors: {len(old_result['errors']) > 0}")
    print()
    
    # New style
    print("2. New Style (class-based):")
    new_result = ExampleValidator.validate_new_style(dataset)
    print(f"   Type: {type(new_result)}")
    print(f"   Result: {new_result}")
    print(f"   Has errors: {new_result.has_errors}")
    print(f"   Is valid: {new_result.is_valid}")
    print(f"   Error count: {new_result.error_count}")
    print()
    
    # Hybrid style
    print("3. Hybrid Style (ValidationResult internal, dict return):")
    hybrid_result = ExampleValidator.validate_hybrid_style(dataset)
    print(f"   Type: {type(hybrid_result)}")
    print(f"   Result: {hybrid_result}")
    print(f"   Compatible with old: {hybrid_result == old_result}")
    print()
    
    # Show ValidationResult advantages
    print("4. ValidationResult Advantages:")
    print(f"   • Type safety: {type(new_result).__name__}")
    print(f"   • Properties: has_errors={new_result.has_errors}, is_valid={new_result.is_valid}")
    print(f"   • Counts: {new_result.error_count} errors, {new_result.warning_count} warnings")
    print(f"   • Methods: add_error(), add_warning(), merge(), clear()")
    print(f"   • Conversion: to_dict() for compatibility")
    print()
    
    # Show merging capability
    print("5. Merging Results:")
    result1 = ValidationResult()
    result1.add_error("Error from validator 1")
    
    result2 = ValidationResult()
    result2.add_warning("Warning from validator 2")
    
    result1.merge(result2)
    print(f"   Merged result: {result1}")
    print(f"   Total issues: {result1.total_issues}")


if __name__ == "__main__":
    demonstrate_migration()