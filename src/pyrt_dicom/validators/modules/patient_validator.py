"""Patient Module DICOM validation - PS3.3 C.7.1.1

This module provides comprehensive validation for the Patient Module as defined in
DICOM PS3.3 Section C.7.1.1. The Patient Module contains attributes of the Patient
that are needed for interpretation of Composite Instances and are common for all
Studies performed on the Patient.

The validator ensures compliance with all DICOM Type 1, Type 2, Type 3, Type 1C,
and Type 2C requirements, including complex conditional logic for non-human organisms,
alternative calendars, responsible persons, and deidentification requirements.
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.patient_enums import ResponsiblePersonRole


class PatientValidator(BaseValidator):
    """Validator for DICOM Patient Module (PS3.3 C.7.1.1).
    
    This validator provides comprehensive validation of all Patient Module requirements
    including Type 1C and Type 2C conditional logic for:
    - Non-human organisms with species, breed, and responsibility requirements
    - Alternative calendar date specifications
    - Responsible person role requirements
    - Patient deidentification method requirements
    - All enumerated value constraints
    - Complex sequence validation
    
    The validator is designed to provide clear, actionable error messages with
    specific DICOM tag references and guidance for resolution.
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Patient Module requirements on any pydicom Dataset.
        
        Performs comprehensive validation of all DICOM Patient Module requirements
        including Type 1, Type 2, Type 3, Type 1C, and Type 2C elements.
        Validates conditional logic, enumerated values, and sequence structures.
        
        Args:
            dataset: pydicom Dataset to validate against Patient Module requirements
            config: Validation configuration options. If None, default configuration is used.
            
        Returns:
            ValidationResult containing any validation errors and warnings with
            specific DICOM tag references and actionable guidance for resolution.
            
        Raises:
            None - All validation issues are captured in the ValidationResult
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 2 required elements (can be empty but must be present)
        if config.validate_required_elements:
            PatientValidator._validate_type_2_requirements(dataset, result)
        
        # Validate Type 1C and 2C conditional requirements
        if config.validate_conditional_requirements:
            PatientValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            PatientValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            PatientValidator._validate_sequence_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_type_2_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 2 elements are present (but may be empty).
        
        Per DICOM PS3.3 C.7.1.1, the Patient Module core elements are Type 2:
        - Patient's Name (0010,0010)
        - Patient ID (0010,0020) 
        - Patient's Birth Date (0010,0030)
        - Patient's Sex (0010,0040)
        """
        type_2_elements = [
            ('PatientName', '(0010,0010)', "Patient's Name"),
            ('PatientID', '(0010,0020)', "Patient ID"),
            ('PatientBirthDate', '(0010,0030)', "Patient's Birth Date"),
            ('PatientSex', '(0010,0040)', "Patient's Sex")
        ]
        
        for attr_name, tag, description in type_2_elements:
            if not hasattr(dataset, attr_name):
                result.add_error(
                    f"{description} {tag} is required (Type 2 element) but is missing. "
                    f"Type 2 elements must be present but may be empty. "
                    f"See DICOM PS3.3 C.7.1.1 for details."
                )
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C and 2C conditional requirements."""
        
        # Type 1C: Alternative Calendar requirement
        # Per DICOM PS3.3 C.7.1.1: Patient's Alternative Calendar (0010,0035) is required if either
        # Patient's Birth Date in Alternative Calendar (0010,0033) or Patient's Death Date 
        # in Alternative Calendar (0010,0034) is present
        has_alt_birth = hasattr(dataset, 'PatientBirthDateInAlternativeCalendar')
        has_alt_death = hasattr(dataset, 'PatientDeathDateInAlternativeCalendar')
        if has_alt_birth or has_alt_death:
            if not hasattr(dataset, 'PatientAlternativeCalendar') or not dataset.PatientAlternativeCalendar:
                result.add_error(
                    "Patient's Alternative Calendar (0010,0035) is required when "
                    "Patient's Birth Date in Alternative Calendar (0010,0033) or "
                    "Patient's Death Date in Alternative Calendar (0010,0034) is present (Type 1C requirement). "
                    "See DICOM PS3.3 C.7.1.1 for details."
                )
        
        # Type 1C/2C: Non-human organism requirements per DICOM PS3.3 C.7.1.1
        is_non_human = (hasattr(dataset, 'PatientSpeciesDescription') or 
                       hasattr(dataset, 'PatientSpeciesCodeSequence'))
        
        if is_non_human:
            # Type 1C: Either species description OR species code sequence required
            has_species_desc = (hasattr(dataset, 'PatientSpeciesDescription') and 
                              getattr(dataset, 'PatientSpeciesDescription', ''))
            has_species_code = (hasattr(dataset, 'PatientSpeciesCodeSequence') and 
                              getattr(dataset, 'PatientSpeciesCodeSequence', []))
            if not has_species_desc and not has_species_code:
                result.add_error(
                    "Patient Species Description (0010,2201) OR Patient Species Code Sequence (0010,2202) "
                    "is required for non-human organisms (Type 1C requirement). "
                    "At least one must be provided. See DICOM PS3.3 C.7.1.1 for details."
                )
            
            # Type 2C: Patient Breed Code Sequence is required (but may be empty)
            if not hasattr(dataset, 'PatientBreedCodeSequence'):
                result.add_error(
                    "Patient Breed Code Sequence (0010,2293) is required for non-human organisms "
                    "(Type 2C requirement), but may be empty. See DICOM PS3.3 C.7.1.1 for details."
                )
            
            # Type 2C: Breed description required if breed code sequence is empty
            breed_desc = getattr(dataset, 'PatientBreedDescription', '')
            breed_seq = getattr(dataset, 'PatientBreedCodeSequence', [])
            if not breed_seq and not breed_desc:
                result.add_error(
                    "Patient Breed Description (0010,2292) is required for non-human organisms "
                    "when Patient Breed Code Sequence (0010,2293) is empty (Type 2C requirement). "
                    "See DICOM PS3.3 C.7.1.1 for details."
                )
            
            # Type 2C: Breed Registration Sequence is required (but may be empty)
            if not hasattr(dataset, 'BreedRegistrationSequence'):
                result.add_error(
                    "Breed Registration Sequence (0010,2294) is required for non-human organisms "
                    "(Type 2C requirement), but may be empty. See DICOM PS3.3 C.7.1.1 for details."
                )
            
            # Type 2C: Responsible Person is required (but may be empty)
            if not hasattr(dataset, 'ResponsiblePerson'):
                result.add_error(
                    "Responsible Person (0010,2297) is required for non-human organisms "
                    "(Type 2C requirement), but may be empty. See DICOM PS3.3 C.7.1.1 for details."
                )
            
            # Type 2C: Responsible Organization is required (but may be empty)
            if not hasattr(dataset, 'ResponsibleOrganization'):
                result.add_error(
                    "Responsible Organization (0010,2299) is required for non-human organisms "
                    "(Type 2C requirement), but may be empty. See DICOM PS3.3 C.7.1.1 for details."
                )
        
        # Type 1C: Responsible Person Role requirement per DICOM PS3.3 C.7.1.1
        resp_person = getattr(dataset, 'ResponsiblePerson', '')
        if resp_person and not hasattr(dataset, 'ResponsiblePersonRole'):
            result.add_error(
                "Responsible Person Role (0010,2298) is required when "
                "Responsible Person (0010,2297) is present and has a value (Type 1C requirement). "
                "See DICOM PS3.3 C.7.1.1.1.2 for defined terms and details."
            )
        
        # Type 1C: De-identification method requirement per DICOM PS3.3 C.7.1.1
        identity_removed = getattr(dataset, 'PatientIdentityRemoved', '')
        if identity_removed == "YES":
            has_method = (hasattr(dataset, 'DeIdentificationMethod') and 
                         getattr(dataset, 'DeIdentificationMethod', ''))
            has_method_seq = (hasattr(dataset, 'DeIdentificationMethodCodeSequence') and 
                            getattr(dataset, 'DeIdentificationMethodCodeSequence', []))
            if not has_method and not has_method_seq:
                result.add_error(
                    "Either De-identification Method (0012,0063) OR De-identification Method Code Sequence (0012,0064) "
                    "is required when Patient Identity Removed (0012,0062) is YES (Type 1C requirement). "
                    "At least one must be provided. See DICOM PS3.3 C.7.1.1 for details."
                )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications."""
        
        # Patient's Sex (0010,0040)
        patient_sex = getattr(dataset, 'PatientSex', '')
        if patient_sex:
            BaseValidator.validate_enumerated_value(
                patient_sex, ["M", "F", "O"], 
                "Patient's Sex (0010,0040)", result
            )
        
        # Quality Control Subject (0010,0200)
        quality_control = getattr(dataset, 'QualityControlSubject', '')
        if quality_control:
            BaseValidator.validate_enumerated_value(
                quality_control, ["YES", "NO"],
                "Quality Control Subject (0010,0200)", result
            )
        
        # Type of Patient ID (0010,0022)
        type_of_id = getattr(dataset, 'TypeOfPatientID', '')
        if type_of_id:
            BaseValidator.validate_enumerated_value(
                type_of_id, ["TEXT", "RFID", "BARCODE"],
                "Type of Patient ID (0010,0022)", result
            )
        
        # Patient Identity Removed (0012,0062)
        identity_removed = getattr(dataset, 'PatientIdentityRemoved', '')
        if identity_removed:
            BaseValidator.validate_enumerated_value(
                identity_removed, ["YES", "NO"],
                "Patient Identity Removed (0012,0062)", result
            )
        
        # Responsible Person Role (0010,2298)
        resp_role = getattr(dataset, 'ResponsiblePersonRole', '')
        if resp_role:
            valid_roles = [role.value for role in ResponsiblePersonRole]
            BaseValidator.validate_enumerated_value(
                resp_role, valid_roles,
                "Responsible Person Role (0010,2298)", result
            )
        
        # Strain Nomenclature (0010,0213)
        strain_nom = getattr(dataset, 'StrainNomenclature', '')
        if strain_nom:
            BaseValidator.validate_enumerated_value(
                strain_nom, ["MGI_2013"],
                "Strain Nomenclature (0010,0213)", result
            )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        # Other Patient IDs Sequence - each item needs Patient ID and Type
        other_ids_seq = getattr(dataset, 'OtherPatientIDsSequence', [])
        for i, item in enumerate(other_ids_seq):
            if not item.get('PatientID'):
                result.add_error(f"Other Patient IDs Sequence item {i}: Patient ID (0010,0020) is required")
            if not item.get('TypeOfPatientID'):
                result.add_error(f"Other Patient IDs Sequence item {i}: Type of Patient ID (0010,0022) is required")
        
        # Strain Stock Sequence - each item needs stock number, source, and registry
        strain_stock_seq = getattr(dataset, 'StrainStockSequence', [])
        for i, item in enumerate(strain_stock_seq):
            if not item.get('StrainStockNumber'):
                result.add_error(f"Strain Stock Sequence item {i}: Strain Stock Number (0010,0214) is required")
            if not item.get('StrainSource'):
                result.add_error(f"Strain Stock Sequence item {i}: Strain Source (0010,0217) is required")
            if not item.get('StrainSourceRegistryCodeSequence'):
                result.add_error(f"Strain Stock Sequence item {i}: Strain Source Registry Code Sequence (0010,0215) is required")
        
        # Genetic Modifications Sequence - each item needs description and nomenclature
        genetic_mod_seq = getattr(dataset, 'GeneticModificationsSequence', [])
        for i, item in enumerate(genetic_mod_seq):
            if not item.get('GeneticModificationsDescription'):
                result.add_error(f"Genetic Modifications Sequence item {i}: Genetic Modifications Description (0010,0222) is required")
            if not item.get('GeneticModificationsNomenclature'):
                result.add_error(f"Genetic Modifications Sequence item {i}: Genetic Modifications Nomenclature (0010,0223) is required")