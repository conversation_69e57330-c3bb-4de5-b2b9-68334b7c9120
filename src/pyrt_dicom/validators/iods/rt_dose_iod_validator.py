"""RT Dose IOD DICOM validation - PS3.3 A.18.3"""

from typing import TYPE_CHECKING
from ..modules.base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult

if TYPE_CHECKING:
    from ...iods.rt_dose_iod import RTDoseIOD


class RTDoseIODValidator(BaseValidator):
    """Validator for DICOM RT Dose IOD (PS3.3 A.18.3)."""
    
    @staticmethod
    def validate(rt_dose_iod: 'RTDoseIOD', config: ValidationConfig | None = None) -> ValidationResult:
        """Validate RT Dose IOD requirements.
        
        Args:
            rt_dose_iod: RTDoseIOD instance to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate base IOD requirements
        rt_dose_iod._validate_base_iod_requirements(result)
        
        # Validate required modules are present
        RTDoseIODValidator._validate_required_modules(rt_dose_iod, result)
        
        # Validate RT-specific requirements
        if config.validate_conditional_requirements:
            RTDoseIODValidator._validate_rt_dose_requirements(rt_dose_iod, result)
        
        # Validate conditional module requirements for grid-based doses
        RTDoseIODValidator._validate_conditional_dependencies(rt_dose_iod, result)
        
        # Validate frame of reference consistency
        RTDoseIODValidator._validate_frame_reference_consistency(rt_dose_iod, result)
        
        # Validate dose parameters
        if config.check_enumerated_values:
            RTDoseIODValidator._validate_dose_parameters(rt_dose_iod, result)
        
        return result
    
    @staticmethod
    def _validate_required_modules(rt_dose_iod: 'RTDoseIOD', result: ValidationResult) -> None:
        """Validate that all required modules are present."""
        required_modules = [
            ('patient', 'Patient Module'),
            ('general_study', 'General Study Module'),
            ('rt_series', 'RT Series Module'),
            ('frame_of_reference', 'Frame of Reference Module'),
            ('general_equipment', 'General Equipment Module'),
            ('rt_dose', 'RT Dose Module'),
            ('sop_common', 'SOP Common Module')
        ]

        for module_key, module_name in required_modules:
            if module_key not in rt_dose_iod._modules:
                result.add_error(f"{module_name} is required for RT Dose IOD")
    
    @staticmethod
    def _validate_rt_dose_requirements(rt_dose_iod: 'RTDoseIOD', result: ValidationResult) -> None:
        """Validate RT Dose-specific requirements."""
        # Validate RT Series modality
        rt_series_module = rt_dose_iod._modules.get('rt_series')
        if rt_series_module:
            modality = getattr(rt_series_module, 'Modality', None)
            if modality != 'RTDOSE':
                result.add_error(f"RT Series Module modality must be 'RTDOSE', got '{modality}'")

        # Validate SOP Class UID
        sop_common_module = rt_dose_iod._modules.get('sop_common')
        if sop_common_module:
            sop_class_uid = getattr(sop_common_module, 'SOPClassUID', None)
            if sop_class_uid != rt_dose_iod.SOP_CLASS_UID:
                result.add_error(
                    f"SOP Class UID must be '{rt_dose_iod.SOP_CLASS_UID}' for RT Dose IOD, got '{sop_class_uid}'"
                )
    
    @staticmethod
    def _validate_conditional_dependencies(rt_dose_iod: 'RTDoseIOD', result: ValidationResult) -> None:
        """Validate conditional module requirements for grid-based doses."""
        rt_dose_module = rt_dose_iod._modules.get('rt_dose')
        if rt_dose_module:
            pixel_data = getattr(rt_dose_module, 'PixelData', None)
            if pixel_data is not None:
                # Grid-based dose data is present, validate conditional modules
                if 'general_image' not in rt_dose_iod._modules:
                    result.add_error(
                        "General Image Module is required when dose data contains grid-based doses"
                    )
                if 'image_plane' not in rt_dose_iod._modules:
                    result.add_error(
                        "Image Plane Module is required when dose data contains grid-based doses"
                    )
                if 'image_pixel' not in rt_dose_iod._modules:
                    result.add_error(
                        "Image Pixel Module is required when dose data contains grid-based doses"
                    )

                # Check for multi-frame requirement
                image_pixel_module = rt_dose_iod._modules.get('image_pixel')
                if image_pixel_module:
                    number_of_frames = getattr(image_pixel_module, 'NumberOfFrames', 1)
                    if number_of_frames > 1 and 'multi_frame' not in rt_dose_iod._modules:
                        result.add_error(
                            "Multi-frame Module is required when dose data contains multi-frame pixel data"
                        )
    
    @staticmethod
    def _validate_frame_reference_consistency(rt_dose_iod: 'RTDoseIOD', result: ValidationResult) -> None:
        """Validate frame of reference consistency."""
        frame_ref_module = rt_dose_iod._modules.get('frame_of_reference')
        if frame_ref_module:
            frame_ref_uid = getattr(frame_ref_module, 'FrameOfReferenceUID', None)
            if not frame_ref_uid:
                result.add_error("Frame of Reference UID is required in Frame of Reference Module")
    
    @staticmethod
    def _validate_dose_parameters(rt_dose_iod: 'RTDoseIOD', result: ValidationResult) -> None:
        """Validate dose units and type."""
        rt_dose_module = rt_dose_iod._modules.get('rt_dose')
        if rt_dose_module:
            # Validate dose units
            dose_units = getattr(rt_dose_module, 'DoseUnits', None)
            if not dose_units:
                result.add_error("Dose Units is required in RT Dose Module")
            elif dose_units not in ['GY', 'RELATIVE']:
                result.add_warning(
                    f"Dose Units '{dose_units}' should typically be 'GY' or 'RELATIVE'"
                )

            # Validate dose type
            dose_type = getattr(rt_dose_module, 'DoseType', None)
            if not dose_type:
                result.add_error("Dose Type is required in RT Dose Module")
            elif dose_type not in ['PHYSICAL', 'EFFECTIVE', 'ERROR']:
                result.add_warning(
                    f"Dose Type '{dose_type}' should be 'PHYSICAL', 'EFFECTIVE', or 'ERROR'"
                )