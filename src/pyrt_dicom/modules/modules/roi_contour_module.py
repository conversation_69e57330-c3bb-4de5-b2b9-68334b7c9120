"""
ROI Contour Module - DICOM PS3.3 C.8.8.6

The ROI Contour Module is used to define ROIs as a set of contours. Each ROI contains 
a sequence of one or more contours, where a contour is either a single point (for a 
point ROI) or more than one point (representing an open or closed polygon).
"""
from pydicom import Dataset
from .base_module import BaseModule
from ...enums.rt_enums import ContourGeometricType
from ...validators.modules.roi_contour_validator import ROIContourValidator
from ...validators.modules.base_validator import ValidationConfig
from ...validators import ValidationResult
from ...utils.dicom_formatters import format_enum_value


class ROIContourModule(BaseModule):
    """ROI Contour Module implementation for DICOM PS3.3 C.8.8.6.
    
    Uses composition with internal dataset management for clean separation of concerns.
    Used to define ROIs as a set of contours with geometric types.
    
    Usage:
        # Create with required elements
        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[
                ROIContourModule.create_roi_contour_item(
                    referenced_roi_number=1,
                    roi_display_color=[255, 0, 0],
                    contour_sequence=[
                        ROIContourModule.create_contour_item(
                            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
                            number_of_contour_points=4,
                            contour_data=[10.0, 10.0, 0.0, 20.0, 10.0, 0.0, 20.0, 20.0, 0.0, 10.0, 20.0, 0.0]
                        )
                    ]
                )
            ]
        )
        
        # Add optional elements
        roi_contour.with_optional_elements(
            # Optional elements can be added here
        )
        
        # Generate dataset for IOD integration
        dataset = roi_contour.to_dataset()
        
        # Validate
        result = roi_contour.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        roi_contour_sequence: list[Dataset]
    ) -> 'ROIContourModule':
        """Create ROIContourModule from all required (Type 1) data elements.
        
        Args:
            roi_contour_sequence (list[Dataset]): Sequence of Contour Sequences defining ROIs (3006,0039) Type 1
                
        Returns:
            ROIContourModule: New module instance with required data elements set
        """
        instance = cls()
        instance._dataset.ROIContourSequence = roi_contour_sequence
        return instance
    
    def with_optional_elements(
        self
    ) -> 'ROIContourModule':
        """Add optional (Type 3) elements.
        
        Note: This module has no Type 3 elements at the top level.
        All optional elements are within the sequences.
        
        Returns:
            ROIContourModule: Self for method chaining
        """
        return self
    
    @staticmethod
    def create_roi_contour_item(
        referenced_roi_number: int,
        roi_display_color: list[int],
        contour_sequence: list[Dataset] | None = None,
        recommended_display_grayscale_value: int | None = None,
        recommended_display_cielab_value: list[float] | None = None,
        source_pixel_planes_characteristics_sequence: list[Dataset] | None = None,
        source_series_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create ROI contour sequence item.

        Args:
            referenced_roi_number (int): Uniquely identifies the referenced ROI (3006,0084) Type 1
            roi_display_color (list[int]): RGB triplet color representation for ROI (3006,002A) Type 1
            contour_sequence (list[Dataset] | None): Sequence of Contours defining ROI (3006,0040) Type 3
            recommended_display_grayscale_value (int | None): Default grayscale value for monochrome display (0062,000C) Type 3
            recommended_display_cielab_value (list[float] | None): Default triplet value for color display (0062,000D) Type 3
            source_pixel_planes_characteristics_sequence (list[Dataset] | None): Characteristics of pixel planes (3006,004A) Type 3
            source_series_sequence (list[Dataset] | None): Image Series on which ROI was defined (3006,004B) Type 3

        Returns:
            Dataset: ROI contour sequence item
        """
        item = Dataset()
        item.ReferencedROINumber = referenced_roi_number
        item.ROIDisplayColor = roi_display_color

        # Add optional elements if provided
        if contour_sequence is not None:
            item.ContourSequence = contour_sequence
        if recommended_display_grayscale_value is not None:
            item.RecommendedDisplayGrayscaleValue = recommended_display_grayscale_value
        if recommended_display_cielab_value is not None:
            item.RecommendedDisplayCIELabValue = recommended_display_cielab_value
        if source_pixel_planes_characteristics_sequence is not None:
            item.SourcePixelPlanesCharacteristicsSequence = source_pixel_planes_characteristics_sequence
        if source_series_sequence is not None:
            item.SourceSeriesSequence = source_series_sequence

        return item
    
    @staticmethod
    def create_contour_item(
        contour_geometric_type: str | ContourGeometricType,
        number_of_contour_points: int,
        contour_data: list[float],
        contour_number: int | None = None,
        contour_image_sequence: list[Dataset] | None = None,
        contour_slab_thickness: float | None = None,
        contour_offset_vector: list[float] | None = None
    ) -> Dataset:
        """Create contour sequence item.

        Args:
            contour_geometric_type (str | ContourGeometricType): Geometric type of contour (3006,0042) Type 1
            number_of_contour_points (int): Number of points (triplets) in Contour Data (3006,0046) Type 1
            contour_data (list[float]): Sequence of (x,y,z) triplets defining contour (3006,0050) Type 1
            contour_number (int | None): Identification number of the contour (3006,0048) Type 3
            contour_image_sequence (list[Dataset] | None): Sequence of images containing contour (3006,0016) Type 3
            contour_slab_thickness (float | None): Thickness of contour slab in mm (3006,0044) Type 3
            contour_offset_vector (list[float] | None): Offset vector for contour (3006,0045) Type 3

        Returns:
            Dataset: Contour sequence item
        """
        item = Dataset()
        item.ContourGeometricType = format_enum_value(contour_geometric_type)
        item.NumberOfContourPoints = number_of_contour_points
        item.ContourData = contour_data

        # Add optional elements if provided
        if contour_number is not None:
            item.ContourNumber = contour_number
        if contour_image_sequence is not None:
            item.ContourImageSequence = contour_image_sequence
        if contour_slab_thickness is not None:
            item.ContourSlabThickness = contour_slab_thickness
        if contour_offset_vector is not None:
            item.ContourOffsetVector = contour_offset_vector

        return item
    
    @staticmethod
    def create_source_pixel_planes_characteristics_item(
        pixel_spacing: list[float],
        spacing_between_slices: float,
        image_orientation_patient: list[float],
        image_position_patient: list[float],
        number_of_frames: int,
        rows: int,
        columns: int
    ) -> Dataset:
        """Create source pixel planes characteristics sequence item.

        Args:
            pixel_spacing (list[float]): Physical distance between pixel centers (0028,0030) Type 1
            spacing_between_slices (float): Spacing between adjacent slices (0018,0088) Type 1
            image_orientation_patient (list[float]): Direction cosines of first row and column (0020,0037) Type 1
            image_position_patient (list[float]): Coordinates of upper left corner (0020,0032) Type 1
            number_of_frames (int): Number of source pixel planes (0028,0008) Type 1
            rows (int): Number of rows in source pixel planes (0028,0010) Type 1
            columns (int): Number of columns in source pixel planes (0028,0011) Type 1

        Returns:
            Dataset: Source pixel planes characteristics sequence item
        """
        item = Dataset()
        item.PixelSpacing = pixel_spacing
        item.SpacingBetweenSlices = spacing_between_slices
        item.ImageOrientationPatient = image_orientation_patient
        item.ImagePositionPatient = image_position_patient
        item.NumberOfFrames = number_of_frames
        item.Rows = rows
        item.Columns = columns
        return item

    @staticmethod
    def create_source_series_item(
        series_instance_uid: str
    ) -> Dataset:
        """Create source series sequence item.

        Args:
            series_instance_uid (str): Unique identifier of the Series (0020,000E) Type 1

        Returns:
            Dataset: Source series sequence item
        """
        item = Dataset()
        item.SeriesInstanceUID = series_instance_uid
        return item

    @staticmethod
    def create_contour_image_item(
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str
    ) -> Dataset:
        """Create contour image sequence item.

        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID (0008,1150) Type 1
            referenced_sop_instance_uid (str): Referenced SOP Instance UID (0008,1155) Type 1

        Returns:
            Dataset: Contour image sequence item
        """
        item = Dataset()
        item.ReferencedSOPClassUID = referenced_sop_class_uid
        item.ReferencedSOPInstanceUID = referenced_sop_instance_uid
        return item

    @staticmethod
    def create_patient_treatment_preparation_item(
        patient_treatment_preparation_procedure_code_sequence: list[Dataset],
        patient_treatment_preparation_method_code_sequence: list[Dataset] | None = None,
        patient_treatment_preparation_device_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create patient treatment preparation sequence item.

        Args:
            patient_treatment_preparation_procedure_code_sequence (list[Dataset]): Procedure codes (0018,002A) Type 1
            patient_treatment_preparation_method_code_sequence (list[Dataset] | None): Method codes (0018,002B) Type 3
            patient_treatment_preparation_device_sequence (list[Dataset] | None): Device sequence (0018,002C) Type 3

        Returns:
            Dataset: Patient treatment preparation sequence item
        """
        item = Dataset()
        item.PatientTreatmentPreparationProcedureCodeSequence = patient_treatment_preparation_procedure_code_sequence

        if patient_treatment_preparation_method_code_sequence is not None:
            item.PatientTreatmentPreparationMethodCodeSequence = patient_treatment_preparation_method_code_sequence
        if patient_treatment_preparation_device_sequence is not None:
            item.PatientTreatmentPreparationDeviceSequence = patient_treatment_preparation_device_sequence

        return item

    @property
    def has_contours(self) -> bool:
        """Check if contour data is present.
        
        Returns:
            bool: True if ROI Contour Sequence is present
        """
        return hasattr(self._dataset, 'ROIContourSequence')
    
    @property
    def roi_count(self) -> int:
        """Get the number of ROIs defined in this module.
        
        Returns:
            int: Number of ROIs in ROI Contour Sequence
        """
        roi_sequence = getattr(self._dataset, 'ROIContourSequence', [])
        return len(roi_sequence)
    
    @property
    def total_contour_count(self) -> int:
        """Get the total number of contours across all ROIs.
        
        Returns:
            int: Total number of contours
        """
        roi_sequence = getattr(self._dataset, 'ROIContourSequence', [])
        total_contours = 0
        for roi_item in roi_sequence:
            contour_sequence = roi_item.get('ContourSequence', [])
            total_contours += len(contour_sequence)
        return total_contours
    
    def get_contour_geometric_types(self) -> list[str]:
        """Get list of contour geometric types present in this module.
        
        Returns:
            list[str]: List of unique contour geometric types
        """
        roi_sequence = getattr(self._dataset, 'ROIContourSequence', [])
        geometric_types = []
        for roi_item in roi_sequence:
            contour_sequence = roi_item.get('ContourSequence', [])
            for contour_item in contour_sequence:
                geometric_type = contour_item.get('ContourGeometricType', '')
                if geometric_type and geometric_type not in geometric_types:
                    geometric_types.append(geometric_type)
        return geometric_types
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this ROI Contour Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return ROIContourValidator.validate(self._dataset, config)
