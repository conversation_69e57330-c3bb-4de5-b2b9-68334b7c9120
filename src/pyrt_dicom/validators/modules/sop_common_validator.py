"""SOP Common Module DICOM validation - PS3.3 C.12.1"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.common_enums import (
    SyntheticData, SOPInstanceStatus, QueryRetrieveView,
    ContentQualification, LongitudinalTemporalInformationModified
)


class SOPCommonValidator(BaseValidator):
    """Validator for DICOM SOP Common Module (PS3.3 C.12.1)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate SOP Common Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1 requirements
        SOPCommonValidator._validate_required_elements(dataset, result)
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            SOPCommonValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            SOPCommonValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            SOPCommonValidator._validate_sequence_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 required elements."""
        
        # Type 1: SOP Class UID (0008,0016)
        if not hasattr(dataset, 'SOPClassUID'):
            result.add_error("SOP Class UID (0008,0016) is required (Type 1)")
        elif not dataset.SOPClassUID:
            result.add_error("SOP Class UID (0008,0016) cannot be empty (Type 1)")
        
        # Type 1: SOP Instance UID (0008,0018)
        if not hasattr(dataset, 'SOPInstanceUID'):
            result.add_error("SOP Instance UID (0008,0018) is required (Type 1)")
        elif not dataset.SOPInstanceUID:
            result.add_error("SOP Instance UID (0008,0018) cannot be empty (Type 1)")
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C and 2C conditional requirements."""
        
        # Type 1C: Specific Character Set required if expanded or replacement character set is used
        # This is difficult to validate automatically without knowing the content, so we'll just warn
        # if non-ASCII characters are detected in string fields
        
        # Type 1C: Coding Scheme Registry required if Coding Scheme is registered
        coding_scheme_seq = getattr(dataset, 'CodingSchemeIdentificationSequence', [])
        for i, item in enumerate(coding_scheme_seq):
            # If coding scheme is registered, registry is required
            if item.get('CodingSchemeUID') and not item.get('CodingSchemeRegistry'):
                result.add_warning(
                    f"Coding Scheme Identification Sequence item {i}: "
                    f"Coding Scheme Registry (0008,0112) may be required if Coding Scheme is registered"
                )
        
        # Type 2C: Coding Scheme External ID required if registered and UID not present
        for i, item in enumerate(coding_scheme_seq):
            if item.get('CodingSchemeRegistry') and not item.get('CodingSchemeUID') and not item.get('CodingSchemeExternalID'):
                result.add_error(
                    f"Coding Scheme Identification Sequence item {i}: "
                    f"Coding Scheme External ID (0008,0114) is required if Coding Scheme is registered "
                    f"and Coding Scheme UID (0008,010C) is not present"
                )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications."""
        
        # Synthetic Data (0008,001C)
        synthetic_data = getattr(dataset, 'SyntheticData', '')
        if synthetic_data:
            valid_values = [val.value for val in SyntheticData]
            BaseValidator.validate_enumerated_value(
                synthetic_data, valid_values,
                "Synthetic Data (0008,001C)", result
            )
        
        # SOP Instance Status (0100,0410)
        sop_status = getattr(dataset, 'SOPInstanceStatus', '')
        if sop_status:
            valid_values = [val.value for val in SOPInstanceStatus]
            BaseValidator.validate_enumerated_value(
                sop_status, valid_values,
                "SOP Instance Status (0100,0410)", result
            )
        
        # Query/Retrieve View (0008,0053)
        query_view = getattr(dataset, 'QueryRetrieveView', '')
        if query_view:
            valid_values = [val.value for val in QueryRetrieveView]
            BaseValidator.validate_enumerated_value(
                query_view, valid_values,
                "Query/Retrieve View (0008,0053)", result
            )
        
        # Content Qualification (0018,9004)
        content_qual = getattr(dataset, 'ContentQualification', '')
        if content_qual:
            valid_values = [val.value for val in ContentQualification]
            BaseValidator.validate_enumerated_value(
                content_qual, valid_values,
                "Content Qualification (0018,9004)", result
            )
        
        # Longitudinal Temporal Information Modified (0028,0303)
        temporal_modified = getattr(dataset, 'LongitudinalTemporalInformationModified', '')
        if temporal_modified:
            valid_values = [val.value for val in LongitudinalTemporalInformationModified]
            BaseValidator.validate_enumerated_value(
                temporal_modified, valid_values,
                "Longitudinal Temporal Information Modified (0028,0303)", result
            )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        # Coding Scheme Identification Sequence - each item needs Coding Scheme Designator
        coding_scheme_seq = getattr(dataset, 'CodingSchemeIdentificationSequence', [])
        for i, item in enumerate(coding_scheme_seq):
            if not item.get('CodingSchemeDesignator'):
                result.add_error(
                    f"Coding Scheme Identification Sequence item {i}: "
                    f"Coding Scheme Designator (0008,0102) is required"
                )
        
        # Contributing Equipment Sequence - each item needs Purpose of Reference and Manufacturer
        contrib_equip_seq = getattr(dataset, 'ContributingEquipmentSequence', [])
        for i, item in enumerate(contrib_equip_seq):
            if not item.get('PurposeOfReferenceCodeSequence'):
                result.add_error(
                    f"Contributing Equipment Sequence item {i}: "
                    f"Purpose of Reference Code Sequence (0040,A170) is required"
                )
            if not item.get('Manufacturer'):
                result.add_error(
                    f"Contributing Equipment Sequence item {i}: "
                    f"Manufacturer (0008,0070) is required"
                )
        
        # Context Group Identification Sequence - each item needs Context Identifier and Mapping Resource
        context_group_seq = getattr(dataset, 'ContextGroupIdentificationSequence', [])
        for i, item in enumerate(context_group_seq):
            if not item.get('ContextIdentifier'):
                result.add_error(
                    f"Context Group Identification Sequence item {i}: "
                    f"Context Identifier (0008,010F) is required"
                )
            if not item.get('MappingResource'):
                result.add_error(
                    f"Context Group Identification Sequence item {i}: "
                    f"Mapping Resource (0008,0105) is required"
                )
            if not item.get('ContextGroupVersion'):
                result.add_error(
                    f"Context Group Identification Sequence item {i}: "
                    f"Context Group Version (0008,0106) is required"
                )
        
        # Mapping Resource Identification Sequence - each item needs Mapping Resource
        mapping_resource_seq = getattr(dataset, 'MappingResourceIdentificationSequence', [])
        for i, item in enumerate(mapping_resource_seq):
            if not item.get('MappingResource'):
                result.add_error(
                    f"Mapping Resource Identification Sequence item {i}: "
                    f"Mapping Resource (0008,0105) is required"
                )
